import React, { useEffect } from 'react';
import { notification } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  InfoCircleOutlined, 
  CloseCircleOutlined 
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppSelector, useAppDispatch } from '@/store';
import { removeNotification } from '@/store/slices/uiSlice';

// 样式组件
const NotificationContent = styled.div`
  .notification-title {
    font-weight: 600;
    color: var(--dataset-text);
    margin-bottom: 4px;
  }
  
  .notification-message {
    color: var(--dataset-text-secondary);
    font-size: 14px;
    line-height: 1.5;
  }
  
  .notification-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    
    .action-button {
      padding: 4px 12px;
      border: 1px solid var(--dataset-border);
      border-radius: var(--dataset-border-radius);
      background: var(--dataset-bg);
      color: var(--dataset-text);
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: var(--dataset-primary);
        color: var(--dataset-primary);
      }
      
      &.primary {
        background: var(--dataset-primary);
        border-color: var(--dataset-primary);
        color: white;
        
        &:hover {
          background: #40a9ff;
        }
      }
    }
  }
`;

// 通知图标映射
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'success':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    case 'warning':
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
    case 'error':
      return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    case 'info':
    default:
      return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
  }
};

// 通知系统组件
const NotificationSystem: React.FC = () => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector(state => state.ui);

  useEffect(() => {
    // 处理新的通知
    notifications.forEach(notif => {
      if (!notif.displayed) {
        const config = {
          key: notif.id,
          message: (
            <NotificationContent>
              <div className="notification-title">{notif.title}</div>
              {notif.message && (
                <div className="notification-message">{notif.message}</div>
              )}
              {notif.actions && notif.actions.length > 0 && (
                <div className="notification-actions">
                  {notif.actions.map((action, index) => (
                    <button
                      key={index}
                      className={`action-button ${action.type || ''}`}
                      onClick={() => {
                        action.onClick?.();
                        if (action.closeOnClick !== false) {
                          notification.close(notif.id);
                          dispatch(removeNotification(notif.id));
                        }
                      }}
                    >
                      {action.label}
                    </button>
                  ))}
                </div>
              )}
            </NotificationContent>
          ),
          description: '',
          icon: getNotificationIcon(notif.type),
          duration: notif.duration || (notif.type === 'error' ? 0 : 4.5),
          placement: notif.placement || 'topRight',
          onClose: () => {
            dispatch(removeNotification(notif.id));
          },
          style: {
            width: notif.width || 400,
          },
        };

        // 根据类型调用不同的通知方法
        switch (notif.type) {
          case 'success':
            notification.success(config);
            break;
          case 'warning':
            notification.warning(config);
            break;
          case 'error':
            notification.error(config);
            break;
          case 'info':
          default:
            notification.info(config);
            break;
        }

        // 标记为已显示
        dispatch({ 
          type: 'ui/markNotificationDisplayed', 
          payload: notif.id 
        });
      }
    });
  }, [notifications, dispatch]);

  return null; // 这个组件不渲染任何内容
};

// 通知工具函数
export const showNotification = {
  success: (title: string, message?: string, options?: any) => ({
    type: 'ui/addNotification',
    payload: {
      type: 'success',
      title,
      message,
      ...options
    }
  }),
  
  error: (title: string, message?: string, options?: any) => ({
    type: 'ui/addNotification',
    payload: {
      type: 'error',
      title,
      message,
      duration: 0, // 错误通知默认不自动关闭
      ...options
    }
  }),
  
  warning: (title: string, message?: string, options?: any) => ({
    type: 'ui/addNotification',
    payload: {
      type: 'warning',
      title,
      message,
      ...options
    }
  }),
  
  info: (title: string, message?: string, options?: any) => ({
    type: 'ui/addNotification',
    payload: {
      type: 'info',
      title,
      message,
      ...options
    }
  }),
  
  // 带操作按钮的通知
  withActions: (type: string, title: string, message: string, actions: Array<{
    label: string;
    onClick?: () => void;
    type?: 'primary' | 'default';
    closeOnClick?: boolean;
  }>) => ({
    type: 'ui/addNotification',
    payload: {
      type,
      title,
      message,
      actions,
      duration: 0 // 有操作按钮的通知不自动关闭
    }
  }),
  
  // 进度通知
  progress: (title: string, progress: number, message?: string) => ({
    type: 'ui/addNotification',
    payload: {
      type: 'info',
      title,
      message: message || `进度: ${progress}%`,
      duration: 0,
      progress
    }
  })
};

// 快捷通知方法
export const notify = {
  success: (message: string) => showNotification.success('操作成功', message),
  error: (message: string) => showNotification.error('操作失败', message),
  warning: (message: string) => showNotification.warning('警告', message),
  info: (message: string) => showNotification.info('提示', message),
  
  // 常用场景的快捷方法
  saved: () => showNotification.success('保存成功', '数据已成功保存'),
  deleted: () => showNotification.success('删除成功', '数据已成功删除'),
  copied: () => showNotification.success('复制成功', '内容已复制到剪贴板'),
  
  networkError: () => showNotification.error('网络错误', '请检查网络连接后重试'),
  permissionDenied: () => showNotification.error('权限不足', '您没有执行此操作的权限'),
  
  confirm: (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => 
    showNotification.withActions('warning', title, message, [
      {
        label: '取消',
        onClick: onCancel,
        closeOnClick: true
      },
      {
        label: '确认',
        type: 'primary',
        onClick: onConfirm,
        closeOnClick: true
      }
    ])
};

export default NotificationSystem;
