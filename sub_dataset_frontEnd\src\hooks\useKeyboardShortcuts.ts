import { useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '@/store';
import { addNotification } from '@/store/slices/uiSlice';

// 快捷键配置接口
interface ShortcutConfig {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  disabled?: boolean;
}

// 快捷键组合
interface KeyCombination {
  key: string;
  ctrlKey: boolean;
  altKey: boolean;
  shiftKey: boolean;
  metaKey: boolean;
}

// 检查快捷键是否匹配
const isShortcutMatch = (event: KeyboardEvent, shortcut: ShortcutConfig): boolean => {
  return (
    event.key.toLowerCase() === shortcut.key.toLowerCase() &&
    !!event.ctrlKey === !!shortcut.ctrlKey &&
    !!event.altKey === !!shortcut.altKey &&
    !!event.shiftKey === !!shortcut.shiftKey &&
    !!event.metaKey === !!shortcut.metaKey
  );
};

// 格式化快捷键显示
const formatShortcut = (shortcut: ShortcutConfig): string => {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.altKey) parts.push('Alt');
  if (shortcut.shiftKey) parts.push('Shift');
  if (shortcut.metaKey) parts.push('Cmd');
  
  parts.push(shortcut.key.toUpperCase());
  
  return parts.join(' + ');
};

// 键盘快捷键Hook
export const useKeyboardShortcuts = (customShortcuts: ShortcutConfig[] = []) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // 默认快捷键配置
  const defaultShortcuts: ShortcutConfig[] = [
    // 导航快捷键
    {
      key: '1',
      ctrlKey: true,
      action: () => navigate('/actions'),
      description: '跳转到Actions页面'
    },
    {
      key: '2',
      ctrlKey: true,
      action: () => navigate('/collections'),
      description: '跳转到Collections页面'
    },
    {
      key: '3',
      ctrlKey: true,
      action: () => navigate('/query-builder'),
      description: '跳转到查询构建器'
    },
    {
      key: '4',
      ctrlKey: true,
      action: () => navigate('/templates'),
      description: '跳转到模板页面'
    },
    
    // 功能快捷键
    {
      key: 'n',
      ctrlKey: true,
      action: () => {
        // 根据当前页面创建新项目
        const path = window.location.pathname;
        if (path.includes('/actions')) {
          navigate('/actions/new');
        } else if (path.includes('/collections')) {
          // 触发创建Collection的操作
          dispatch(addNotification({
            type: 'info',
            title: '快捷键',
            message: '创建新Collection功能'
          }));
        }
      },
      description: '创建新项目'
    },
    
    {
      key: 's',
      ctrlKey: true,
      action: () => {
        // 保存当前编辑内容
        dispatch(addNotification({
          type: 'info',
          title: '快捷键',
          message: '保存功能'
        }));
      },
      description: '保存当前内容'
    },
    
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        // 聚焦到搜索框
        const searchInput = document.querySelector('input[placeholder*="搜索"], input[placeholder*="search"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
          searchInput.select();
        }
      },
      description: '聚焦搜索框'
    },
    
    {
      key: 'r',
      ctrlKey: true,
      action: () => {
        // 刷新当前页面数据
        window.location.reload();
      },
      description: '刷新页面'
    },
    
    {
      key: 'Enter',
      ctrlKey: true,
      action: () => {
        // 执行当前Action或Collection
        dispatch(addNotification({
          type: 'info',
          title: '快捷键',
          message: '执行功能'
        }));
      },
      description: '执行当前项目'
    },
    
    // 编辑器快捷键
    {
      key: 'Tab',
      action: () => {
        // 在编辑器中插入缩进
        // 这个会在特定的编辑器组件中处理
      },
      description: '插入缩进'
    },
    
    {
      key: 'z',
      ctrlKey: true,
      action: () => {
        // 撤销操作
        dispatch(addNotification({
          type: 'info',
          title: '快捷键',
          message: '撤销功能'
        }));
      },
      description: '撤销'
    },
    
    {
      key: 'y',
      ctrlKey: true,
      action: () => {
        // 重做操作
        dispatch(addNotification({
          type: 'info',
          title: '快捷键',
          message: '重做功能'
        }));
      },
      description: '重做'
    },
    
    // 帮助快捷键
    {
      key: '?',
      shiftKey: true,
      action: () => {
        showShortcutHelp();
      },
      description: '显示快捷键帮助'
    },
    
    {
      key: 'Escape',
      action: () => {
        // 关闭模态框或取消当前操作
        const modals = document.querySelectorAll('.ant-modal-mask');
        if (modals.length > 0) {
          const closeButton = document.querySelector('.ant-modal-close') as HTMLElement;
          if (closeButton) {
            closeButton.click();
          }
        }
      },
      description: '关闭模态框/取消操作'
    }
  ];

  // 显示快捷键帮助
  const showShortcutHelp = useCallback(() => {
    const allShortcuts = [...defaultShortcuts, ...customShortcuts];
    const helpContent = allShortcuts
      .filter(shortcut => !shortcut.disabled && shortcut.description)
      .map(shortcut => `${formatShortcut(shortcut)}: ${shortcut.description}`)
      .join('\n');

    dispatch(addNotification({
      type: 'info',
      title: '键盘快捷键',
      message: helpContent,
      duration: 0,
      width: 500
    }));
  }, [defaultShortcuts, customShortcuts, dispatch]);

  // 处理键盘事件
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 如果焦点在输入框中，跳过某些快捷键
    const activeElement = document.activeElement;
    const isInputFocused = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.getAttribute('contenteditable') === 'true'
    );

    // 合并所有快捷键
    const allShortcuts = [...defaultShortcuts, ...customShortcuts];

    for (const shortcut of allShortcuts) {
      if (shortcut.disabled) continue;

      if (isShortcutMatch(event, shortcut)) {
        // 对于某些快捷键，即使在输入框中也要执行
        const alwaysExecute = ['Escape', '?'].includes(shortcut.key) || 
                             (shortcut.ctrlKey && ['s', 'z', 'y'].includes(shortcut.key));

        if (!isInputFocused || alwaysExecute) {
          event.preventDefault();
          event.stopPropagation();
          
          try {
            shortcut.action();
          } catch (error) {
            console.error('快捷键执行错误:', error);
            dispatch(addNotification({
              type: 'error',
              title: '快捷键错误',
              message: '执行快捷键时发生错误'
            }));
          }
          break;
        }
      }
    }
  }, [defaultShortcuts, customShortcuts, dispatch]);

  // 注册键盘事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // 返回工具函数
  return {
    showShortcutHelp,
    formatShortcut,
    shortcuts: [...defaultShortcuts, ...customShortcuts]
  };
};

// 快捷键帮助组件Hook
export const useShortcutHelp = () => {
  const { showShortcutHelp, shortcuts } = useKeyboardShortcuts();

  const getShortcutsByCategory = () => {
    const categories = {
      navigation: shortcuts.filter(s => s.description.includes('跳转')),
      editing: shortcuts.filter(s => ['保存', '撤销', '重做', '创建'].some(word => s.description.includes(word))),
      general: shortcuts.filter(s => !s.description.includes('跳转') && 
                                   !['保存', '撤销', '重做', '创建'].some(word => s.description.includes(word)))
    };

    return categories;
  };

  return {
    showShortcutHelp,
    getShortcutsByCategory,
    shortcuts
  };
};

export default useKeyboardShortcuts;
