import React, { useState, useEffect } from 'react';
import {
  Card,
  Typo<PERSON>,
  Button,
  Form,
  Input,
  Space,
  Tabs,
  Row,
  Col,
  Divider,
  Switch,
  InputNumber,
  Alert,
  Tag,
  Tooltip,
  List,
  Modal,
  Tree
} from 'antd';
import {
  CodeOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  CopyOutlined,
  FormatPainterOutlined,
  BugOutlined,
  FunctionOutlined,
  VariableOutlined,
  FileTextOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { Action } from '@/types/action';
import { useAppDispatch } from '@/store';
import { updateActionInPlace } from '@/store/slices/actionSlice';
import { addNotification } from '@/store/slices/uiSlice';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

// 样式组件
const EditorContainer = styled.div`
  padding: 24px;
  background: var(--dataset-bg);
  min-height: 100%;
`;

const EditorTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 24px;
  }
`;

const CodeEditorWrapper = styled.div`
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  overflow: hidden;

  .code-toolbar {
    background: var(--dataset-bg-secondary);
    border-bottom: 1px solid var(--dataset-border);
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .code-editor {
    background: var(--dataset-bg);

    textarea {
      border: none;
      background: transparent;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
      resize: vertical;
      min-height: 400px;

      &:focus {
        box-shadow: none;
      }
    }
  }
`;

const FunctionList = styled.div`
  .function-item {
    padding: 12px;
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    margin-bottom: 12px;
    background: var(--dataset-bg);

    .function-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .function-name {
        font-weight: 600;
        color: var(--dataset-text);
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .function-actions {
        display: flex;
        gap: 4px;
      }
    }

    .function-description {
      color: var(--dataset-text-secondary);
      font-size: 12px;
      margin-bottom: 8px;
    }

    .function-signature {
      background: var(--dataset-bg-secondary);
      padding: 8px;
      border-radius: var(--dataset-border-radius);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: var(--dataset-text-secondary);
    }
  }
`;

const VariableList = styled.div`
  .variable-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    margin-bottom: 8px;
    background: var(--dataset-bg);

    .variable-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .variable-name {
        font-weight: 500;
        color: var(--dataset-text);
      }

      .variable-type {
        font-size: 12px;
      }

      .variable-value {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        color: var(--dataset-text-secondary);
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .variable-actions {
      display: flex;
      gap: 4px;
    }
  }
`;

const ExecutionResult = styled.div`
  background: var(--dataset-bg-secondary);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  padding: 16px;
  margin-top: 16px;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .result-status {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .result-content {
    background: var(--dataset-bg);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
  }
`;

// 默认JS代码模板
const DEFAULT_JS_CODE = `export default {
  // 变量定义
  myVar1: "Hello World",
  myVar2: 123,

  // 异步函数示例
  myFunction1: async () => {
    // 在这里编写您的异步代码
    try {
      // 例如：调用API
      // const response = await Api1.run();
      // return response.data;

      return "Hello from async function!";
    } catch (error) {
      console.error("Error in myFunction1:", error);
      throw error;
    }
  },

  // 同步函数示例
  myFunction2: () => {
    // 在这里编写您的同步代码
    return "Hello from sync function!";
  },

  // 数据处理函数
  processData: (data) => {
    if (!Array.isArray(data)) {
      return [];
    }

    return data.map(item => ({
      ...item,
      processed: true,
      timestamp: new Date().toISOString()
    }));
  },

  // 表单验证函数
  validateForm: (formData) => {
    const errors = {};

    if (!formData.name || formData.name.trim().length < 2) {
      errors.name = "名称至少需要2个字符";
    }

    if (!formData.email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {
      errors.email = "请输入有效的邮箱地址";
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};`;

interface JSEditorProps {
  action?: Action;
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const JSEditor: React.FC<JSEditorProps> = ({ action, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const [activeTab, setActiveTab] = useState('editor');
  const [jsCode, setJsCode] = useState(DEFAULT_JS_CODE);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<any>(null);
  const [functions, setFunctions] = useState<any[]>([]);
  const [variables, setVariables] = useState<any[]>([]);
  const [selectedFunction, setSelectedFunction] = useState<string>('');

  // 初始化表单数据
  useEffect(() => {
    if (action) {
      const code = action.actionConfiguration.jsFunction || DEFAULT_JS_CODE;
      form.setFieldsValue({
        name: action.name,
        jsFunction: code,
        timeout: action.actionConfiguration.timeout || 5000,
        executeOnLoad: action.executeOnLoad,
      });
      setJsCode(code);
      parseJSCode(code);
    } else {
      parseJSCode(DEFAULT_JS_CODE);
    }
  }, [action, form]);

  // 解析JS代码，提取函数和变量
  const parseJSCode = (code: string) => {
    try {
      // 简单的正则解析（实际项目中可以使用AST解析）
      const functionMatches = code.match(/(\w+):\s*(async\s+)?\([^)]*\)\s*=>/g) || [];
      const variableMatches = code.match(/(\w+):\s*[^,\n}]+(?=,|\n|})/g) || [];

      const parsedFunctions = functionMatches.map((match, index) => {
        const name = match.split(':')[0].trim();
        const isAsync = match.includes('async');
        return {
          id: `func_${index}`,
          name,
          isAsync,
          description: `${isAsync ? '异步' : '同步'}函数`,
          signature: match
        };
      });

      const parsedVariables = variableMatches
        .filter(match => !functionMatches.some(func => func.includes(match.split(':')[0])))
        .map((match, index) => {
          const [name, value] = match.split(':').map(s => s.trim());
          let type = 'unknown';
          if (value.startsWith('"') || value.startsWith("'")) type = 'string';
          else if (!isNaN(Number(value))) type = 'number';
          else if (value === 'true' || value === 'false') type = 'boolean';
          else if (value.startsWith('[')) type = 'array';
          else if (value.startsWith('{')) type = 'object';

          return {
            id: `var_${index}`,
            name,
            type,
            value: value.length > 50 ? value.substring(0, 50) + '...' : value
          };
        });

      setFunctions(parsedFunctions);
      setVariables(parsedVariables);
    } catch (error) {
      console.error('Error parsing JS code:', error);
    }
  };

  // 格式化代码
  const formatCode = () => {
    // 简单的代码格式化
    const formatted = jsCode
      .replace(/\s*{\s*/g, ' {\n  ')
      .replace(/\s*}\s*/g, '\n}\n')
      .replace(/,\s*/g, ',\n  ')
      .replace(/;\s*/g, ';\n  ')
      .trim();
    setJsCode(formatted);
    form.setFieldValue('jsFunction', formatted);
  };

  // 执行JS函数
  const executeFunction = async (functionName?: string) => {
    setIsExecuting(true);
    try {
      // 模拟JS函数执行
      await new Promise(resolve => setTimeout(resolve, 800));

      const mockResult = {
        success: true,
        functionName: functionName || 'myFunction1',
        executionTime: '156ms',
        result: functionName === 'validateForm'
          ? { isValid: true, errors: {} }
          : functionName === 'processData'
          ? [{ id: 1, name: 'Test', processed: true, timestamp: new Date().toISOString() }]
          : "Function executed successfully!",
        logs: [
          { level: 'info', message: 'Function execution started', timestamp: new Date().toISOString() },
          { level: 'info', message: 'Function execution completed', timestamp: new Date().toISOString() }
        ]
      };

      setExecutionResult(mockResult);
      setActiveTab('result');

      dispatch(addNotification({
        type: 'success',
        title: '执行成功',
        message: `函数 ${mockResult.functionName} 执行完成`,
      }));
    } catch (error) {
      setExecutionResult({
        success: false,
        error: error,
        logs: [
          { level: 'error', message: `Execution failed: ${error}`, timestamp: new Date().toISOString() }
        ]
      });

      dispatch(addNotification({
        type: 'error',
        title: '执行失败',
        message: error as string,
      }));
    } finally {
      setIsExecuting(false);
    }
  };

  // 保存JS代码
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      const updatedAction = {
        ...action,
        name: values.name,
        executeOnLoad: values.executeOnLoad,
        actionConfiguration: {
          ...action?.actionConfiguration,
          jsFunction: values.jsFunction,
          timeout: values.timeout,
        },
        timeoutInMillisecond: values.timeout,
        updatedAt: new Date().toISOString(),
      };

      if (action?.id) {
        dispatch(updateActionInPlace({ id: action.id, ...updatedAction }));
      }

      dispatch(addNotification({
        type: 'success',
        title: '保存成功',
        message: 'JavaScript代码已保存',
      }));

      if (onSave) {
        onSave(updatedAction);
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '保存失败',
        message: '请检查表单输入',
      }));
    }
  };

  return (
    <EditorContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
          <CodeOutlined />
          JavaScript编辑器
        </Title>
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={isExecuting}
            onClick={() => executeFunction(selectedFunction)}
          >
            执行函数
          </Button>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            保存
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={() => {
              navigator.clipboard.writeText(jsCode);
              dispatch(addNotification({
                type: 'success',
                title: '复制成功',
                message: 'JavaScript代码已复制到剪贴板',
              }));
            }}
          >
            复制
          </Button>
        </Space>
      </div>

      <EditorTabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="代码编辑器" key="editor">
          <Form form={form} layout="vertical">
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="JS对象名称"
                  name="name"
                  rules={[{ required: true, message: '请输入JS对象名称' }]}
                >
                  <Input placeholder="输入JS对象名称" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="超时时间(ms)"
                  name="timeout"
                >
                  <InputNumber
                    min={1000}
                    max={60000}
                    style={{ width: '100%' }}
                    placeholder="5000"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="页面加载时执行"
                  name="executeOnLoad"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <CodeEditorWrapper>
            <div className="code-toolbar">
              <div className="toolbar-left">
                <Text strong>JavaScript代码</Text>
                <Tag color="orange">ES6+</Tag>
              </div>
              <div className="toolbar-right">
                <Tooltip title="格式化代码">
                  <Button
                    size="small"
                    icon={<FormatPainterOutlined />}
                    onClick={formatCode}
                  />
                </Tooltip>
                <Tooltip title="语法检查">
                  <Button
                    size="small"
                    icon={<BugOutlined />}
                  />
                </Tooltip>
              </div>
            </div>
            <div className="code-editor">
              <Form.Item
                name="jsFunction"
                rules={[{ required: true, message: '请输入JavaScript代码' }]}
              >
                <TextArea
                  value={jsCode}
                  onChange={(e) => {
                    setJsCode(e.target.value);
                    parseJSCode(e.target.value);
                  }}
                  placeholder="输入JavaScript代码..."
                  autoSize={{ minRows: 20, maxRows: 30 }}
                />
              </Form.Item>
            </div>
          </CodeEditorWrapper>
        </TabPane>

        <TabPane tab="函数列表" key="functions">
          <div style={{ marginBottom: 16 }}>
            <Title level={5}>
              <FunctionOutlined /> 函数列表 ({functions.length})
            </Title>
          </div>
          <FunctionList>
            {functions.map(func => (
              <div key={func.id} className="function-item">
                <div className="function-header">
                  <div className="function-name">
                    <FunctionOutlined />
                    {func.name}
                    {func.isAsync && <Tag color="blue" size="small">async</Tag>}
                  </div>
                  <div className="function-actions">
                    <Button
                      size="small"
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={() => {
                        setSelectedFunction(func.name);
                        executeFunction(func.name);
                      }}
                    >
                      执行
                    </Button>
                  </div>
                </div>
                <div className="function-description">{func.description}</div>
                <div className="function-signature">{func.signature}</div>
              </div>
            ))}
            {functions.length === 0 && (
              <Alert
                type="info"
                message="暂无函数"
                description="在代码编辑器中定义函数后，它们将在这里显示"
                showIcon
              />
            )}
          </FunctionList>
        </TabPane>

        <TabPane tab="变量列表" key="variables">
          <div style={{ marginBottom: 16 }}>
            <Title level={5}>
              <VariableOutlined /> 变量列表 ({variables.length})
            </Title>
          </div>
          <VariableList>
            {variables.map(variable => (
              <div key={variable.id} className="variable-item">
                <div className="variable-info">
                  <div className="variable-name">{variable.name}</div>
                  <Tag className="variable-type" color="geekblue" size="small">
                    {variable.type}
                  </Tag>
                  <div className="variable-value">{variable.value}</div>
                </div>
                <div className="variable-actions">
                  <Button size="small" icon={<EditOutlined />} />
                  <Button size="small" icon={<CopyOutlined />} />
                </div>
              </div>
            ))}
            {variables.length === 0 && (
              <Alert
                type="info"
                message="暂无变量"
                description="在代码编辑器中定义变量后，它们将在这里显示"
                showIcon
              />
            )}
          </VariableList>
        </TabPane>

        <TabPane tab="执行结果" key="result">
          {executionResult ? (
            <ExecutionResult>
              <div className="result-header">
                <div className="result-status">
                  <Tag color={executionResult.success ? 'success' : 'error'}>
                    {executionResult.success ? '执行成功' : '执行失败'}
                  </Tag>
                  {executionResult.functionName && (
                    <Text type="secondary">
                      函数: {executionResult.functionName}
                    </Text>
                  )}
                  {executionResult.executionTime && (
                    <Text type="secondary">
                      执行时间: {executionResult.executionTime}
                    </Text>
                  )}
                </div>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(executionResult.result, null, 2));
                    dispatch(addNotification({
                      type: 'success',
                      title: '复制成功',
                      message: '执行结果已复制到剪贴板',
                    }));
                  }}
                >
                  复制结果
                </Button>
              </div>
              <div className="result-content">
                {executionResult.success
                  ? JSON.stringify(executionResult.result, null, 2)
                  : executionResult.error
                }
              </div>
              {executionResult.logs && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>执行日志</Title>
                  <List
                    size="small"
                    dataSource={executionResult.logs}
                    renderItem={(log: any) => (
                      <List.Item>
                        <Tag color={log.level === 'error' ? 'red' : 'blue'}>
                          {log.level}
                        </Tag>
                        <Text style={{ fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace', fontSize: 12 }}>
                          {log.message}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </Text>
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </ExecutionResult>
          ) : (
            <Alert
              type="info"
              message="暂无执行结果"
              description="点击执行函数按钮来运行JavaScript代码并查看结果"
              showIcon
            />
          )}
        </TabPane>
      </EditorTabs>
    </EditorContainer>
  );
};

export default JSEditor;
