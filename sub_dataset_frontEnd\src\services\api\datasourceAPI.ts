import { 
  mockDatasources, 
  mockDatasourceTestResults, 
  mockDatasourceStats,
  Datasource,
  DatasourceTestResult,
  DatasourceStats
} from '../mockData/datasources';

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 数据源API服务
export class DatasourceAPI {
  // 获取所有数据源
  static async getAllDatasources(): Promise<Datasource[]> {
    await delay(300);
    return [...mockDatasources];
  }

  // 根据ID获取数据源
  static async getDatasourceById(id: string): Promise<Datasource | null> {
    await delay(200);
    const datasource = mockDatasources.find(ds => ds.id === id);
    return datasource ? { ...datasource } : null;
  }

  // 创建数据源
  static async createDatasource(datasource: Omit<Datasource, 'id' | 'createdAt' | 'updatedAt'>): Promise<Datasource> {
    await delay(500);
    
    const newDatasource: Datasource = {
      ...datasource,
      id: `ds_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockDatasources.push(newDatasource);
    return { ...newDatasource };
  }

  // 更新数据源
  static async updateDatasource(id: string, updates: Partial<Datasource>): Promise<Datasource | null> {
    await delay(400);
    
    const index = mockDatasources.findIndex(ds => ds.id === id);
    if (index === -1) return null;

    const updatedDatasource = {
      ...mockDatasources[index],
      ...updates,
      id, // 确保ID不被覆盖
      updatedAt: new Date().toISOString(),
    };

    mockDatasources[index] = updatedDatasource;
    return { ...updatedDatasource };
  }

  // 删除数据源
  static async deleteDatasource(id: string): Promise<boolean> {
    await delay(300);
    
    const index = mockDatasources.findIndex(ds => ds.id === id);
    if (index === -1) return false;

    mockDatasources.splice(index, 1);
    return true;
  }

  // 测试数据源连接
  static async testDatasourceConnection(id: string): Promise<DatasourceTestResult> {
    await delay(1500); // 模拟连接测试时间
    
    const datasource = mockDatasources.find(ds => ds.id === id);
    if (!datasource) {
      throw new Error('数据源不存在');
    }

    // 模拟测试结果
    const isSuccess = Math.random() > 0.1; // 90% 成功率
    const responseTime = Math.floor(Math.random() * 1000) + 50;

    const testResult: DatasourceTestResult = {
      datasourceId: id,
      isSuccess,
      message: isSuccess ? '连接成功' : '连接失败，请检查配置',
      details: isSuccess ? {
        responseTime,
        version: this.getMockVersion(datasource.type),
        features: this.getMockFeatures(datasource.type)
      } : {
        responseTime: 30000
      },
      testedAt: new Date().toISOString()
    };

    // 更新数据源的测试状态
    const index = mockDatasources.findIndex(ds => ds.id === id);
    if (index !== -1) {
      mockDatasources[index] = {
        ...mockDatasources[index],
        isValid: isSuccess,
        lastTestedAt: testResult.testedAt
      };
    }

    // 更新模拟测试结果
    mockDatasourceTestResults[id] = testResult;

    return testResult;
  }

  // 获取数据源统计信息
  static async getDatasourceStats(id: string): Promise<DatasourceStats | null> {
    await delay(250);
    
    const stats = mockDatasourceStats[id];
    return stats ? { ...stats } : null;
  }

  // 获取数据源的表结构（仅适用于数据库类型）
  static async getDatasourceSchema(id: string): Promise<any> {
    await delay(800);
    
    const datasource = mockDatasources.find(ds => ds.id === id);
    if (!datasource) {
      throw new Error('数据源不存在');
    }

    if (!['postgresql', 'mysql', 'mongodb'].includes(datasource.type)) {
      throw new Error('该数据源类型不支持表结构查询');
    }

    // 模拟表结构数据
    return {
      database: datasource.configuration.database,
      tables: [
        {
          name: 'users',
          type: 'table',
          columns: [
            { name: 'id', type: 'integer', nullable: false, primaryKey: true },
            { name: 'name', type: 'varchar(255)', nullable: false },
            { name: 'email', type: 'varchar(255)', nullable: false, unique: true },
            { name: 'created_at', type: 'timestamp', nullable: false },
            { name: 'updated_at', type: 'timestamp', nullable: false }
          ]
        },
        {
          name: 'orders',
          type: 'table',
          columns: [
            { name: 'id', type: 'integer', nullable: false, primaryKey: true },
            { name: 'user_id', type: 'integer', nullable: false, foreignKey: 'users.id' },
            { name: 'total_amount', type: 'decimal(10,2)', nullable: false },
            { name: 'status', type: 'varchar(50)', nullable: false },
            { name: 'created_at', type: 'timestamp', nullable: false }
          ]
        },
        {
          name: 'products',
          type: 'table',
          columns: [
            { name: 'id', type: 'integer', nullable: false, primaryKey: true },
            { name: 'name', type: 'varchar(255)', nullable: false },
            { name: 'price', type: 'decimal(10,2)', nullable: false },
            { name: 'category', type: 'varchar(100)', nullable: true },
            { name: 'in_stock', type: 'boolean', nullable: false, default: true }
          ]
        }
      ]
    };
  }

  // 执行查询（仅适用于数据库类型）
  static async executeQuery(id: string, query: string): Promise<any> {
    await delay(600);
    
    const datasource = mockDatasources.find(ds => ds.id === id);
    if (!datasource) {
      throw new Error('数据源不存在');
    }

    if (!datasource.isValid) {
      throw new Error('数据源连接无效，请先测试连接');
    }

    // 模拟查询结果
    const mockResult = {
      success: true,
      rowCount: Math.floor(Math.random() * 100) + 1,
      executionTime: `${Math.floor(Math.random() * 500) + 50}ms`,
      columns: ['id', 'name', 'email', 'created_at'],
      data: Array.from({ length: 5 }, (_, i) => ({
        id: i + 1,
        name: `用户${i + 1}`,
        email: `user${i + 1}@example.com`,
        created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      }))
    };

    return mockResult;
  }

  // 获取模拟版本信息
  private static getMockVersion(type: string): string {
    const versions: Record<string, string> = {
      postgresql: 'PostgreSQL 14.5',
      mysql: 'MySQL 8.0.32',
      mongodb: 'MongoDB 6.0.3',
      redis: 'Redis 7.0.5',
      elasticsearch: 'Elasticsearch 8.5.0',
      rest_api: 'v2.1.0',
      graphql: 'GraphQL 16.6.0'
    };
    return versions[type] || 'Unknown';
  }

  // 获取模拟功能特性
  private static getMockFeatures(type: string): string[] {
    const features: Record<string, string[]> = {
      postgresql: ['SSL', 'Connection Pooling', 'Prepared Statements', 'JSON Support'],
      mysql: ['SSL', 'Connection Pooling', 'Replication', 'Partitioning'],
      mongodb: ['Sharding', 'Replica Sets', 'GridFS', 'Aggregation Pipeline'],
      redis: ['Persistence', 'Clustering', 'Pub/Sub', 'Lua Scripting'],
      elasticsearch: ['Full-text Search', 'Aggregations', 'Machine Learning', 'Security'],
      rest_api: ['REST API', 'JWT Authentication', 'Rate Limiting', 'Swagger Documentation'],
      graphql: ['Introspection', 'Subscriptions', 'Federation', 'Schema Stitching']
    };
    return features[type] || [];
  }

  // 批量测试数据源连接
  static async batchTestDatasources(ids: string[]): Promise<DatasourceTestResult[]> {
    await delay(2000);
    
    const results: DatasourceTestResult[] = [];
    
    for (const id of ids) {
      try {
        const result = await this.testDatasourceConnection(id);
        results.push(result);
      } catch (error) {
        results.push({
          datasourceId: id,
          isSuccess: false,
          message: error instanceof Error ? error.message : '未知错误',
          testedAt: new Date().toISOString()
        });
      }
    }
    
    return results;
  }

  // 获取数据源类型列表
  static async getDatasourceTypes(): Promise<Array<{
    type: string;
    name: string;
    description: string;
    icon: string;
    category: string;
  }>> {
    await delay(100);
    
    return [
      {
        type: 'postgresql',
        name: 'PostgreSQL',
        description: '开源关系型数据库',
        icon: '🐘',
        category: '关系型数据库'
      },
      {
        type: 'mysql',
        name: 'MySQL',
        description: '流行的关系型数据库',
        icon: '🐬',
        category: '关系型数据库'
      },
      {
        type: 'mongodb',
        name: 'MongoDB',
        description: '文档型NoSQL数据库',
        icon: '🍃',
        category: 'NoSQL数据库'
      },
      {
        type: 'redis',
        name: 'Redis',
        description: '内存键值存储数据库',
        icon: '🔴',
        category: 'NoSQL数据库'
      },
      {
        type: 'elasticsearch',
        name: 'Elasticsearch',
        description: '分布式搜索和分析引擎',
        icon: '🔍',
        category: '搜索引擎'
      },
      {
        type: 'rest_api',
        name: 'REST API',
        description: 'RESTful API服务',
        icon: '🌐',
        category: 'API服务'
      },
      {
        type: 'graphql',
        name: 'GraphQL',
        description: 'GraphQL API服务',
        icon: '⚡',
        category: 'API服务'
      }
    ];
  }
}

export default DatasourceAPI;
