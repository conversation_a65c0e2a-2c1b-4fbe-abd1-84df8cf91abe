import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Form,
  Input,
  Select,
  Space,
  Tabs,
  Table,
  Row,
  Col,
  Divider,
  Switch,
  InputNumber,
  Alert,
  Tag,
  Tooltip
} from 'antd';
import {
  ApiOutlined,
  PlusOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  CopyOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { Action, HttpMethod, ActionType } from '@/types/action';
import { useAppDispatch } from '@/store';
import { updateActionInPlace } from '@/store/slices/actionSlice';
import { addNotification } from '@/store/slices/uiSlice';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

// 样式组件
const EditorContainer = styled.div`
  padding: 24px;
  background: var(--dataset-bg);
  min-height: 100%;
`;

const EditorTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 24px;
  }
`;

const FormSection = styled.div`
  margin-bottom: 24px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-text);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const HeaderTable = styled(Table)`
  .ant-table-thead > tr > th {
    background: var(--dataset-bg-secondary);
  }
`;

const ResponsePreview = styled.div`
  background: var(--dataset-bg-secondary);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  padding: 16px;
  margin-top: 16px;

  .response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .response-status {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .response-body {
    background: var(--dataset-bg);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
  }
`;

// HTTP方法选项
const HTTP_METHODS = [
  { value: HttpMethod.GET, label: 'GET', color: 'blue' },
  { value: HttpMethod.POST, label: 'POST', color: 'green' },
  { value: HttpMethod.PUT, label: 'PUT', color: 'orange' },
  { value: HttpMethod.PATCH, label: 'PATCH', color: 'purple' },
  { value: HttpMethod.DELETE, label: 'DELETE', color: 'red' },
];

interface APIEditorProps {
  action?: Action;
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const APIEditor: React.FC<APIEditorProps> = ({ action, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const [activeTab, setActiveTab] = useState('config');
  const [headers, setHeaders] = useState<Array<{key: string, value: string, description?: string}>>([]);
  const [queryParams, setQueryParams] = useState<Array<{key: string, value: string, description?: string}>>([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [response, setResponse] = useState<any>(null);

  // 初始化表单数据
  useEffect(() => {
    if (action) {
      form.setFieldsValue({
        name: action.name,
        httpMethod: action.actionConfiguration.httpMethod || HttpMethod.GET,
        path: action.actionConfiguration.path || '',
        body: action.actionConfiguration.body || '',
        timeout: action.actionConfiguration.timeout || 10000,
        executeOnLoad: action.executeOnLoad,
      });

      // 设置headers
      if (action.actionConfiguration.headers) {
        setHeaders(action.actionConfiguration.headers);
      }

      // 设置查询参数
      if (action.actionConfiguration.queryParameters) {
        setQueryParams(action.actionConfiguration.queryParameters);
      }
    }
  }, [action, form]);

  // 添加Header
  const addHeader = () => {
    setHeaders([...headers, { key: '', value: '', description: '' }]);
  };

  // 删除Header
  const removeHeader = (index: number) => {
    const newHeaders = headers.filter((_, i) => i !== index);
    setHeaders(newHeaders);
  };

  // 更新Header
  const updateHeader = (index: number, field: string, value: string) => {
    const newHeaders = [...headers];
    newHeaders[index] = { ...newHeaders[index], [field]: value };
    setHeaders(newHeaders);
  };

  // 添加查询参数
  const addQueryParam = () => {
    setQueryParams([...queryParams, { key: '', value: '', description: '' }]);
  };

  // 删除查询参数
  const removeQueryParam = (index: number) => {
    const newParams = queryParams.filter((_, i) => i !== index);
    setQueryParams(newParams);
  };

  // 更新查询参数
  const updateQueryParam = (index: number, field: string, value: string) => {
    const newParams = [...queryParams];
    newParams[index] = { ...newParams[index], [field]: value };
    setQueryParams(newParams);
  };

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      const updatedAction = {
        ...action,
        name: values.name,
        executeOnLoad: values.executeOnLoad,
        actionConfiguration: {
          ...action?.actionConfiguration,
          httpMethod: values.httpMethod,
          path: values.path,
          body: values.body,
          timeout: values.timeout,
          headers: headers.filter(h => h.key && h.value),
          queryParameters: queryParams.filter(p => p.key && p.value),
        },
        timeoutInMillisecond: values.timeout,
        updatedAt: new Date().toISOString(),
      };

      if (action?.id) {
        dispatch(updateActionInPlace({ id: action.id, ...updatedAction }));
      }

      dispatch(addNotification({
        type: 'success',
        title: '保存成功',
        message: 'API配置已保存',
      }));

      if (onSave) {
        onSave(updatedAction);
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '保存失败',
        message: '请检查表单输入',
      }));
    }
  };

  // 执行API
  const handleExecute = async () => {
    setIsExecuting(true);
    try {
      // 模拟API执行
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockResponse = {
        status: 200,
        statusText: 'OK',
        headers: {
          'content-type': 'application/json',
          'content-length': '156'
        },
        data: {
          success: true,
          message: 'API executed successfully',
          timestamp: new Date().toISOString(),
          data: [
            { id: 1, name: 'Test Item 1' },
            { id: 2, name: 'Test Item 2' }
          ]
        }
      };

      setResponse(mockResponse);
      setActiveTab('response');

      dispatch(addNotification({
        type: 'success',
        title: '执行成功',
        message: 'API执行完成',
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '执行失败',
        message: error as string,
      }));
    } finally {
      setIsExecuting(false);
    }
  };

  // Header表格列定义
  const headerColumns = [
    {
      title: 'Key',
      dataIndex: 'key',
      width: '30%',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          placeholder="Header名称"
          onChange={(e) => updateHeader(index, 'key', e.target.value)}
        />
      ),
    },
    {
      title: 'Value',
      dataIndex: 'value',
      width: '40%',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          placeholder="Header值"
          onChange={(e) => updateHeader(index, 'value', e.target.value)}
        />
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      width: '25%',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          placeholder="描述（可选）"
          onChange={(e) => updateHeader(index, 'description', e.target.value)}
        />
      ),
    },
    {
      title: '操作',
      width: '5%',
      render: (text: string, record: any, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeHeader(index)}
        />
      ),
    },
  ];

  // 查询参数表格列定义
  const paramColumns = [
    {
      title: 'Key',
      dataIndex: 'key',
      width: '30%',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          placeholder="参数名称"
          onChange={(e) => updateQueryParam(index, 'key', e.target.value)}
        />
      ),
    },
    {
      title: 'Value',
      dataIndex: 'value',
      width: '40%',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          placeholder="参数值"
          onChange={(e) => updateQueryParam(index, 'value', e.target.value)}
        />
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      width: '25%',
      render: (text: string, record: any, index: number) => (
        <Input
          value={text}
          placeholder="描述（可选）"
          onChange={(e) => updateQueryParam(index, 'description', e.target.value)}
        />
      ),
    },
    {
      title: '操作',
      width: '5%',
      render: (text: string, record: any, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeQueryParam(index)}
        />
      ),
    },
  ];

  return (
    <EditorContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
          <ApiOutlined />
          API编辑器
        </Title>
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={isExecuting}
            onClick={handleExecute}
          >
            执行
          </Button>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            保存
          </Button>
          <Button
            icon={<CopyOutlined />}
          >
            复制
          </Button>
        </Space>
      </div>

      <EditorTabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="配置" key="config">
          <Form form={form} layout="vertical">
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="API名称"
                  name="name"
                  rules={[{ required: true, message: '请输入API名称' }]}
                >
                  <Input placeholder="输入API名称" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="HTTP方法"
                  name="httpMethod"
                  rules={[{ required: true, message: '请选择HTTP方法' }]}
                >
                  <Select placeholder="选择HTTP方法">
                    {HTTP_METHODS.map(method => (
                      <Option key={method.value} value={method.value}>
                        <Tag color={method.color}>{method.label}</Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="超时时间(ms)"
                  name="timeout"
                >
                  <InputNumber
                    min={1000}
                    max={60000}
                    style={{ width: '100%' }}
                    placeholder="10000"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="API路径"
              name="path"
              rules={[{ required: true, message: '请输入API路径' }]}
            >
              <Input placeholder="例如: /api/users" />
            </Form.Item>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="页面加载时执行"
                  name="executeOnLoad"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <Divider />

          <FormSection>
            <div className="section-title">
              请求头 (Headers)
              <Button
                type="dashed"
                size="small"
                icon={<PlusOutlined />}
                onClick={addHeader}
              >
                添加Header
              </Button>
            </div>
            <HeaderTable
              columns={headerColumns}
              dataSource={headers}
              pagination={false}
              size="small"
              rowKey={(record, index) => index?.toString() || '0'}
            />
          </FormSection>

          <FormSection>
            <div className="section-title">
              查询参数 (Query Parameters)
              <Button
                type="dashed"
                size="small"
                icon={<PlusOutlined />}
                onClick={addQueryParam}
              >
                添加参数
              </Button>
            </div>
            <HeaderTable
              columns={paramColumns}
              dataSource={queryParams}
              pagination={false}
              size="small"
              rowKey={(record, index) => index?.toString() || '0'}
            />
          </FormSection>

          <FormSection>
            <div className="section-title">
              请求体 (Request Body)
            </div>
            <Form.Item name="body">
              <TextArea
                rows={8}
                placeholder="输入请求体内容（JSON格式）"
                style={{ fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace' }}
              />
            </Form.Item>
          </FormSection>
        </TabPane>

        <TabPane tab="响应结果" key="response">
          {response ? (
            <ResponsePreview>
              <div className="response-header">
                <div className="response-status">
                  <Tag color={response.status === 200 ? 'success' : 'error'}>
                    {response.status} {response.statusText}
                  </Tag>
                  <Text type="secondary">
                    响应时间: 245ms | 大小: 1.2KB
                  </Text>
                </div>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(response.data, null, 2));
                    dispatch(addNotification({
                      type: 'success',
                      title: '复制成功',
                      message: '响应数据已复制到剪贴板',
                    }));
                  }}
                >
                  复制响应
                </Button>
              </div>
              <div className="response-body">
                {JSON.stringify(response.data, null, 2)}
              </div>
            </ResponsePreview>
          ) : (
            <Alert
              type="info"
              message="暂无响应数据"
              description="点击执行按钮来运行API并查看响应结果"
              showIcon
            />
          )}
        </TabPane>

        <TabPane tab="文档" key="docs">
          <Alert
            type="info"
            message="API文档"
            description="API文档功能正在开发中，将提供自动生成的API文档和测试界面。"
            showIcon
          />
        </TabPane>
      </EditorTabs>
    </EditorContainer>
  );
};

export default APIEditor;
