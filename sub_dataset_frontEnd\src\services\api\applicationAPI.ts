import { 
  mockApplications, 
  mockPages, 
  mockWidgets, 
  mockApplicationStats,
  Application,
  Page,
  Widget,
  ApplicationStats
} from '../mockData/applications';

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 应用程序API服务
export class ApplicationAPI {
  // 获取所有应用程序
  static async getAllApplications(): Promise<Application[]> {
    await delay(300);
    return [...mockApplications];
  }

  // 根据ID获取应用程序
  static async getApplicationById(id: string): Promise<Application | null> {
    await delay(200);
    const app = mockApplications.find(app => app.id === id);
    return app ? { ...app } : null;
  }

  // 创建应用程序
  static async createApplication(application: Omit<Application, 'id' | 'createdAt' | 'updatedAt'>): Promise<Application> {
    await delay(500);
    
    const newApplication: Application = {
      ...application,
      id: `app_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockApplications.push(newApplication);
    return { ...newApplication };
  }

  // 更新应用程序
  static async updateApplication(id: string, updates: Partial<Application>): Promise<Application | null> {
    await delay(400);
    
    const index = mockApplications.findIndex(app => app.id === id);
    if (index === -1) return null;

    const updatedApplication = {
      ...mockApplications[index],
      ...updates,
      id, // 确保ID不被覆盖
      updatedAt: new Date().toISOString(),
    };

    mockApplications[index] = updatedApplication;
    return { ...updatedApplication };
  }

  // 删除应用程序
  static async deleteApplication(id: string): Promise<boolean> {
    await delay(300);
    
    const index = mockApplications.findIndex(app => app.id === id);
    if (index === -1) return false;

    mockApplications.splice(index, 1);
    return true;
  }

  // 获取应用程序统计信息
  static async getApplicationStats(id: string): Promise<ApplicationStats | null> {
    await delay(250);
    
    const stats = mockApplicationStats[id];
    return stats ? { ...stats } : null;
  }

  // 发布应用程序
  static async publishApplication(id: string): Promise<{ success: boolean; url?: string }> {
    await delay(1000);
    
    const app = mockApplications.find(app => app.id === id);
    if (!app) {
      throw new Error('应用程序不存在');
    }

    // 模拟发布过程
    const success = Math.random() > 0.1; // 90% 成功率
    
    if (success) {
      return {
        success: true,
        url: `https://app.example.com/${app.slug}`
      };
    } else {
      throw new Error('发布失败，请稍后重试');
    }
  }

  // 克隆应用程序
  static async cloneApplication(id: string, name: string): Promise<Application> {
    await delay(800);
    
    const originalApp = mockApplications.find(app => app.id === id);
    if (!originalApp) {
      throw new Error('原应用程序不存在');
    }

    const clonedApp: Application = {
      ...originalApp,
      id: `app_${Date.now()}`,
      name,
      slug: `${originalApp.slug}-copy`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockApplications.push(clonedApp);
    return { ...clonedApp };
  }
}

// 页面API服务
export class PageAPI {
  // 获取应用程序的所有页面
  static async getPagesByApplicationId(applicationId: string): Promise<Page[]> {
    await delay(200);
    return mockPages.filter(page => page.applicationId === applicationId);
  }

  // 根据ID获取页面
  static async getPageById(id: string): Promise<Page | null> {
    await delay(150);
    const page = mockPages.find(page => page.id === id);
    return page ? { ...page } : null;
  }

  // 创建页面
  static async createPage(page: Omit<Page, 'id' | 'createdAt' | 'updatedAt'>): Promise<Page> {
    await delay(400);
    
    const newPage: Page = {
      ...page,
      id: `page_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockPages.push(newPage);
    
    // 更新应用程序的页面列表
    const appIndex = mockApplications.findIndex(app => app.id === page.applicationId);
    if (appIndex !== -1) {
      mockApplications[appIndex].pages.push(newPage.id);
    }

    return { ...newPage };
  }

  // 更新页面
  static async updatePage(id: string, updates: Partial<Page>): Promise<Page | null> {
    await delay(350);
    
    const index = mockPages.findIndex(page => page.id === id);
    if (index === -1) return null;

    const updatedPage = {
      ...mockPages[index],
      ...updates,
      id, // 确保ID不被覆盖
      updatedAt: new Date().toISOString(),
    };

    mockPages[index] = updatedPage;
    return { ...updatedPage };
  }

  // 删除页面
  static async deletePage(id: string): Promise<boolean> {
    await delay(250);
    
    const pageIndex = mockPages.findIndex(page => page.id === id);
    if (pageIndex === -1) return false;

    const page = mockPages[pageIndex];
    mockPages.splice(pageIndex, 1);

    // 从应用程序的页面列表中移除
    const appIndex = mockApplications.findIndex(app => app.id === page.applicationId);
    if (appIndex !== -1) {
      const pageIdIndex = mockApplications[appIndex].pages.indexOf(id);
      if (pageIdIndex !== -1) {
        mockApplications[appIndex].pages.splice(pageIdIndex, 1);
      }
    }

    return true;
  }

  // 复制页面
  static async clonePage(id: string, name: string): Promise<Page> {
    await delay(600);
    
    const originalPage = mockPages.find(page => page.id === id);
    if (!originalPage) {
      throw new Error('原页面不存在');
    }

    const clonedPage: Page = {
      ...originalPage,
      id: `page_${Date.now()}`,
      name,
      slug: `${originalPage.slug}-copy`,
      isHomePage: false, // 复制的页面不能是首页
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockPages.push(clonedPage);
    
    // 更新应用程序的页面列表
    const appIndex = mockApplications.findIndex(app => app.id === originalPage.applicationId);
    if (appIndex !== -1) {
      mockApplications[appIndex].pages.push(clonedPage.id);
    }

    return { ...clonedPage };
  }

  // 设置首页
  static async setHomePage(applicationId: string, pageId: string): Promise<boolean> {
    await delay(200);
    
    // 检查页面是否存在且属于该应用
    const page = mockPages.find(p => p.id === pageId && p.applicationId === applicationId);
    if (!page) return false;

    // 取消其他页面的首页状态
    mockPages.forEach(p => {
      if (p.applicationId === applicationId) {
        p.isHomePage = p.id === pageId;
      }
    });

    // 更新应用程序的首页ID
    const appIndex = mockApplications.findIndex(app => app.id === applicationId);
    if (appIndex !== -1) {
      mockApplications[appIndex].homePageId = pageId;
    }

    return true;
  }
}

// 组件API服务
export class WidgetAPI {
  // 获取页面的所有组件
  static async getWidgetsByPageId(pageId: string): Promise<Widget[]> {
    await delay(150);
    return mockWidgets.filter(widget => widget.pageId === pageId);
  }

  // 根据ID获取组件
  static async getWidgetById(id: string): Promise<Widget | null> {
    await delay(100);
    const widget = mockWidgets.find(widget => widget.id === id);
    return widget ? { ...widget } : null;
  }

  // 创建组件
  static async createWidget(widget: Omit<Widget, 'id' | 'createdAt' | 'updatedAt'>): Promise<Widget> {
    await delay(300);
    
    const newWidget: Widget = {
      ...widget,
      id: `widget_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockWidgets.push(newWidget);
    
    // 更新页面的组件列表
    const pageIndex = mockPages.findIndex(page => page.id === widget.pageId);
    if (pageIndex !== -1) {
      mockPages[pageIndex].widgets.push(newWidget.id);
    }

    return { ...newWidget };
  }

  // 更新组件
  static async updateWidget(id: string, updates: Partial<Widget>): Promise<Widget | null> {
    await delay(250);
    
    const index = mockWidgets.findIndex(widget => widget.id === id);
    if (index === -1) return null;

    const updatedWidget = {
      ...mockWidgets[index],
      ...updates,
      id, // 确保ID不被覆盖
      updatedAt: new Date().toISOString(),
    };

    mockWidgets[index] = updatedWidget;
    return { ...updatedWidget };
  }

  // 删除组件
  static async deleteWidget(id: string): Promise<boolean> {
    await delay(200);
    
    const widgetIndex = mockWidgets.findIndex(widget => widget.id === id);
    if (widgetIndex === -1) return false;

    const widget = mockWidgets[widgetIndex];
    mockWidgets.splice(widgetIndex, 1);

    // 从页面的组件列表中移除
    const pageIndex = mockPages.findIndex(page => page.id === widget.pageId);
    if (pageIndex !== -1) {
      const widgetIdIndex = mockPages[pageIndex].widgets.indexOf(id);
      if (widgetIdIndex !== -1) {
        mockPages[pageIndex].widgets.splice(widgetIdIndex, 1);
      }
    }

    return true;
  }

  // 批量更新组件位置
  static async updateWidgetPositions(updates: Array<{ id: string; position: Widget['position'] }>): Promise<boolean> {
    await delay(300);
    
    for (const update of updates) {
      const index = mockWidgets.findIndex(widget => widget.id === update.id);
      if (index !== -1) {
        mockWidgets[index] = {
          ...mockWidgets[index],
          position: update.position,
          updatedAt: new Date().toISOString(),
        };
      }
    }

    return true;
  }
}

export { ApplicationAPI as default, PageAPI, WidgetAPI };
