import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Row,
  Col,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  Tabs,
  Alert,
  Tooltip,
  Dropdown,
  Menu
} from 'antd';
import {
  FileTextOutlined,
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  MoreOutlined,
  ApiOutlined,
  DatabaseOutlined,
  CodeOutlined,
  StarOutlined,
  StarFilled
} from '@ant-design/icons';
import styled from 'styled-components';

import { PageContainer } from '@/styles/global';
import { useAppDispatch } from '@/store';
import { addNotification } from '@/store/slices/uiSlice';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

// 样式组件
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;

  .header-left {
    .page-title {
      margin: 0;
      color: var(--dataset-text);
    }

    .page-description {
      color: var(--dataset-text-secondary);
      margin-top: 4px;
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
`;

const FilterSection = styled.div`
  margin-bottom: 24px;
  padding: 0 24px;

  .filter-row {
    display: flex;
    gap: 16px;
    align-items: center;
  }
`;

const TemplateGrid = styled.div`
  padding: 0 24px;
`;

const TemplateCard = styled(Card)`
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--dataset-shadow-hover);
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .template-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;

    .template-icon {
      font-size: 24px;
      margin-right: 12px;

      &.api { color: #1890ff; }
      &.db { color: #52c41a; }
      &.js { color: #faad14; }
    }

    .template-title {
      flex: 1;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--dataset-text);
    }

    .template-actions {
      opacity: 0;
      transition: opacity 0.3s ease;
      display: flex;
      gap: 4px;
    }
  }

  &:hover .template-actions {
    opacity: 1;
  }

  .template-description {
    color: var(--dataset-text-secondary);
    margin-bottom: 16px;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--dataset-text-secondary);
      font-size: 12px;
    }
  }

  .template-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-bottom: 12px;
  }

  .template-code-preview {
    background: var(--dataset-bg-secondary);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
    color: var(--dataset-text-secondary);
    max-height: 60px;
    overflow: hidden;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(transparent, var(--dataset-bg-secondary));
    }
  }
`;

// 模拟模板数据
const mockTemplates = [
  {
    id: 'template_1',
    name: '用户列表查询',
    description: '查询用户列表，支持分页和搜索',
    type: 'API',
    category: 'REST API',
    tags: ['用户', '分页', '搜索'],
    isStarred: true,
    usageCount: 156,
    createdAt: '2024-01-15',
    code: `{
  "method": "GET",
  "path": "/api/users",
  "params": {
    "page": "{{Table1.pageNo}}",
    "limit": "{{Table1.pageSize}}",
    "search": "{{SearchInput.text}}"
  }
}`,
    variables: [
      { name: 'page', type: 'number', description: '页码' },
      { name: 'limit', type: 'number', description: '每页数量' },
      { name: 'search', type: 'string', description: '搜索关键词' }
    ]
  },
  {
    id: 'template_2',
    name: '订单统计查询',
    description: 'SQL查询订单统计数据，按日期分组',
    type: 'DB',
    category: 'SQL',
    tags: ['订单', '统计', '分组'],
    isStarred: false,
    usageCount: 89,
    createdAt: '2024-01-16',
    code: `SELECT
  DATE(created_at) as order_date,
  COUNT(*) as order_count,
  SUM(total_amount) as total_revenue
FROM orders
WHERE created_at >= '{{startDate}}'
  AND created_at <= '{{endDate}}'
GROUP BY DATE(created_at)
ORDER BY order_date DESC;`,
    variables: [
      { name: 'startDate', type: 'date', description: '开始日期' },
      { name: 'endDate', type: 'date', description: '结束日期' }
    ]
  },
  {
    id: 'template_3',
    name: '数据处理函数',
    description: 'JavaScript函数用于处理和转换数据',
    type: 'JS',
    category: 'JavaScript',
    tags: ['数据处理', '转换', '验证'],
    isStarred: true,
    usageCount: 234,
    createdAt: '2024-01-17',
    code: `export default {
  processData: (data) => {
    return data.map(item => ({
      ...item,
      fullName: \`\${item.firstName} \${item.lastName}\`,
      isActive: item.status === 'active'
    }));
  },

  validateForm: (formData) => {
    const errors = {};
    if (!formData.email) {
      errors.email = '邮箱不能为空';
    }
    return { isValid: Object.keys(errors).length === 0, errors };
  }
};`,
    variables: [
      { name: 'data', type: 'array', description: '输入数据数组' },
      { name: 'formData', type: 'object', description: '表单数据对象' }
    ]
  },
  {
    id: 'template_4',
    name: 'GraphQL产品查询',
    description: 'GraphQL查询产品信息，支持过滤和分页',
    type: 'API',
    category: 'GraphQL',
    tags: ['产品', 'GraphQL', '过滤'],
    isStarred: false,
    usageCount: 67,
    createdAt: '2024-01-18',
    code: `query GetProducts($first: Int!, $filter: ProductFilter) {
  products(first: $first, filter: $filter) {
    edges {
      node {
        id
        name
        price
        category {
          name
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}`,
    variables: [
      { name: 'first', type: 'number', description: '查询数量' },
      { name: 'filter', type: 'object', description: '过滤条件' }
    ]
  }
];

const TemplatesPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [showStarredOnly, setShowStarredOnly] = useState(false);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 过滤模板
  const filteredTemplates = mockTemplates.filter(template => {
    if (searchText && !template.name.toLowerCase().includes(searchText.toLowerCase()) &&
        !template.description.toLowerCase().includes(searchText.toLowerCase())) {
      return false;
    }
    if (selectedCategory && template.category !== selectedCategory) {
      return false;
    }
    if (selectedType && template.type !== selectedType) {
      return false;
    }
    if (showStarredOnly && !template.isStarred) {
      return false;
    }
    return true;
  });

  // 获取模板图标
  const getTemplateIcon = (type: string) => {
    switch (type) {
      case 'API':
        return <ApiOutlined className="template-icon api" />;
      case 'DB':
        return <DatabaseOutlined className="template-icon db" />;
      case 'JS':
        return <CodeOutlined className="template-icon js" />;
      default:
        return <FileTextOutlined className="template-icon" />;
    }
  };

  // 切换收藏状态
  const toggleStar = (templateId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    // 这里应该调用API更新收藏状态
    dispatch(addNotification({
      type: 'success',
      title: '操作成功',
      message: '模板收藏状态已更新',
    }));
  };

  // 使用模板
  const useTemplate = (template: any, e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(addNotification({
      type: 'success',
      title: '模板已应用',
      message: `模板 "${template.name}" 已应用到编辑器`,
    }));
  };

  // 查看模板详情
  const viewTemplate = (template: any) => {
    setSelectedTemplate(template);
    setIsDetailModalVisible(true);
  };

  // 创建新模板
  const handleCreateTemplate = async () => {
    try {
      const values = await form.validateFields();
      console.log('Creating template:', values);

      dispatch(addNotification({
        type: 'success',
        title: '创建成功',
        message: '模板已成功创建',
      }));

      setIsCreateModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // 渲染模板卡片
  const renderTemplateCard = (template: any) => (
    <Col xs={24} sm={12} lg={8} xl={6} key={template.id}>
      <TemplateCard onClick={() => viewTemplate(template)}>
        <div className="template-header">
          {getTemplateIcon(template.type)}
          <Title level={5} className="template-title">
            {template.name}
          </Title>
          <div className="template-actions">
            <Tooltip title={template.isStarred ? '取消收藏' : '收藏'}>
              <Button
                type="text"
                size="small"
                icon={template.isStarred ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                onClick={(e) => toggleStar(template.id, e)}
              />
            </Tooltip>
            <Tooltip title="使用模板">
              <Button
                type="text"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={(e) => useTemplate(template, e)}
              />
            </Tooltip>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'edit',
                    label: '编辑',
                    icon: <EditOutlined />,
                  },
                  {
                    key: 'copy',
                    label: '复制',
                    icon: <CopyOutlined />,
                  },
                  {
                    type: 'divider',
                  },
                  {
                    key: 'delete',
                    label: '删除',
                    icon: <DeleteOutlined />,
                    danger: true,
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button
                type="text"
                size="small"
                icon={<MoreOutlined />}
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        </div>

        <Paragraph className="template-description">
          {template.description}
        </Paragraph>

        <div className="template-tags">
          {template.tags.map((tag: string) => (
            <Tag key={tag} size="small" color="blue">
              {tag}
            </Tag>
          ))}
        </div>

        <div className="template-meta">
          <div className="meta-item">
            <PlayCircleOutlined />
            <span>使用 {template.usageCount} 次</span>
          </div>
          <div className="meta-item">
            <span>{template.createdAt}</span>
          </div>
        </div>

        <div className="template-code-preview">
          {template.code}
        </div>
      </TemplateCard>
    </Col>
  );

  return (
    <PageContainer>
      {/* 页面头部 */}
      <PageHeader>
        <div className="header-left">
          <Title level={2} className="page-title">查询模板</Title>
          <div className="page-description">
            管理和使用预定义的查询模板，快速创建API、数据库查询和JavaScript函数
          </div>
        </div>
        <div className="header-right">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            创建模板
          </Button>
        </div>
      </PageHeader>

      {/* 过滤器 */}
      <FilterSection>
        <div className="filter-row">
          <Search
            placeholder="搜索模板名称或描述"
            allowClear
            style={{ width: 300 }}
            onSearch={setSearchText}
            enterButton={<SearchOutlined />}
          />
          <Select
            placeholder="选择类型"
            allowClear
            style={{ width: 120 }}
            value={selectedType}
            onChange={setSelectedType}
          >
            <Option value="API">API</Option>
            <Option value="DB">数据库</Option>
            <Option value="JS">JavaScript</Option>
          </Select>
          <Select
            placeholder="选择分类"
            allowClear
            style={{ width: 150 }}
            value={selectedCategory}
            onChange={setSelectedCategory}
          >
            <Option value="REST API">REST API</Option>
            <Option value="GraphQL">GraphQL</Option>
            <Option value="SQL">SQL</Option>
            <Option value="JavaScript">JavaScript</Option>
          </Select>
          <Button
            type={showStarredOnly ? 'primary' : 'default'}
            icon={<StarOutlined />}
            onClick={() => setShowStarredOnly(!showStarredOnly)}
          >
            仅显示收藏
          </Button>
        </div>
      </FilterSection>

      {/* 模板网格 */}
      <TemplateGrid>
        {filteredTemplates.length > 0 ? (
          <Row gutter={[16, 16]}>
            {filteredTemplates.map(renderTemplateCard)}
          </Row>
        ) : (
          <div style={{ textAlign: 'center', padding: '60px 20px' }}>
            <FileTextOutlined style={{ fontSize: 64, color: 'var(--dataset-text-disabled)', marginBottom: 16 }} />
            <div style={{ fontSize: 16, color: 'var(--dataset-text)', marginBottom: 8 }}>
              暂无匹配的模板
            </div>
            <div style={{ color: 'var(--dataset-text-secondary)', marginBottom: 24 }}>
              尝试调整搜索条件或创建新的模板
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsCreateModalVisible(true)}
            >
              创建模板
            </Button>
          </div>
        )}
      </TemplateGrid>

      {/* 创建模板弹窗 */}
      <Modal
        title="创建查询模板"
        open={isCreateModalVisible}
        onOk={handleCreateTemplate}
        onCancel={() => {
          setIsCreateModalVisible(false);
          form.resetFields();
        }}
        width={800}
        okText="创建"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="模板名称"
                name="name"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="输入模板名称" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="模板类型"
                name="type"
                rules={[{ required: true, message: '请选择模板类型' }]}
              >
                <Select placeholder="选择类型">
                  <Option value="API">API</Option>
                  <Option value="DB">数据库</Option>
                  <Option value="JS">JavaScript</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="分类"
                name="category"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="选择分类">
                  <Option value="REST API">REST API</Option>
                  <Option value="GraphQL">GraphQL</Option>
                  <Option value="SQL">SQL</Option>
                  <Option value="JavaScript">JavaScript</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="模板描述"
            name="description"
            rules={[{ required: true, message: '请输入模板描述' }]}
          >
            <Input placeholder="输入模板描述" />
          </Form.Item>

          <Form.Item
            label="标签"
            name="tags"
          >
            <Select
              mode="tags"
              placeholder="输入标签（按回车添加）"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="模板代码"
            name="code"
            rules={[{ required: true, message: '请输入模板代码' }]}
          >
            <TextArea
              rows={12}
              placeholder="输入模板代码..."
              style={{ fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace' }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 模板详情弹窗 */}
      <Modal
        title={selectedTemplate?.name}
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        width={900}
        footer={[
          <Button key="cancel" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button key="use" type="primary" icon={<PlayCircleOutlined />}>
            使用模板
          </Button>,
        ]}
      >
        {selectedTemplate && (
          <Tabs defaultActiveKey="code">
            <TabPane tab="代码" key="code">
              <div style={{
                background: 'var(--dataset-bg-secondary)',
                border: '1px solid var(--dataset-border)',
                borderRadius: 'var(--dataset-border-radius)',
                padding: 16,
                fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                fontSize: 12,
                whiteSpace: 'pre-wrap',
                maxHeight: 400,
                overflow: 'auto'
              }}>
                {selectedTemplate.code}
              </div>
            </TabPane>
            <TabPane tab="变量" key="variables">
              {selectedTemplate.variables?.length > 0 ? (
                <div>
                  {selectedTemplate.variables.map((variable: any, index: number) => (
                    <div key={index} style={{
                      padding: 12,
                      border: '1px solid var(--dataset-border)',
                      borderRadius: 'var(--dataset-border-radius)',
                      marginBottom: 8,
                      background: 'var(--dataset-bg)'
                    }}>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>
                        {variable.name}
                        <Tag size="small" color="blue" style={{ marginLeft: 8 }}>
                          {variable.type}
                        </Tag>
                      </div>
                      <div style={{ color: 'var(--dataset-text-secondary)', fontSize: 12 }}>
                        {variable.description}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert
                  type="info"
                  message="该模板没有定义变量"
                  showIcon
                />
              )}
            </TabPane>
            <TabPane tab="信息" key="info">
              <div>
                <p><strong>类型:</strong> {selectedTemplate.type}</p>
                <p><strong>分类:</strong> {selectedTemplate.category}</p>
                <p><strong>描述:</strong> {selectedTemplate.description}</p>
                <p><strong>使用次数:</strong> {selectedTemplate.usageCount}</p>
                <p><strong>创建时间:</strong> {selectedTemplate.createdAt}</p>
                <p><strong>标签:</strong></p>
                <Space wrap>
                  {selectedTemplate.tags.map((tag: string) => (
                    <Tag key={tag} color="blue">{tag}</Tag>
                  ))}
                </Space>
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </PageContainer>
  );
};

export default TemplatesPage;
