import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Row,
  Col,
  Select,
  Input,
  Space,
  Tabs,
  Tree,
  Table,
  Tag,
  Alert,
  Divider,
  Tooltip,
  Modal
} from 'antd';
import {
  DatabaseOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  TableOutlined,
  FieldStringOutlined,
  NumberOutlined,
  CalendarOutlined,
  BooleanOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  GroupOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { PageContainer, CardContainer } from '@/styles/global';
import QueryBuilder from '@/components/QueryBuilder';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// 样式组件
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;

  .header-left {
    .page-title {
      margin: 0;
      color: var(--dataset-text);
    }

    .page-description {
      color: var(--dataset-text-secondary);
      margin-top: 4px;
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
`;

const BuilderLayout = styled.div`
  display: flex;
  height: calc(100vh - 200px);
  gap: 16px;
  padding: 0 24px;
`;

const LeftPanel = styled(Card)`
  width: 300px;
  height: 100%;
  overflow-y: auto;

  .panel-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--dataset-text);
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
`;

const MainPanel = styled(Card)`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;

  .builder-content {
    flex: 1;
    overflow: hidden;
  }
`;

const RightPanel = styled(Card)`
  width: 350px;
  height: 100%;
  overflow-y: auto;
`;

const TableTree = styled(Tree)`
  .ant-tree-node-content-wrapper {
    display: flex;
    align-items: center;

    .table-icon {
      margin-right: 8px;
      color: var(--dataset-primary);
    }

    .field-icon {
      margin-right: 8px;
      color: var(--dataset-text-secondary);
    }
  }
`;

const QueryPreview = styled.div`
  background: var(--dataset-bg-secondary);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  padding: 16px;
  margin-top: 16px;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .preview-title {
      font-weight: 600;
      color: var(--dataset-text);
    }
  }

  .preview-code {
    background: var(--dataset-bg);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
  }
`;

// 模拟数据库表结构
const mockTables = [
  {
    key: 'users',
    title: 'users',
    icon: <TableOutlined className="table-icon" />,
    children: [
      { key: 'users.id', title: 'id', icon: <NumberOutlined className="field-icon" /> },
      { key: 'users.name', title: 'name', icon: <FieldStringOutlined className="field-icon" /> },
      { key: 'users.email', title: 'email', icon: <FieldStringOutlined className="field-icon" /> },
      { key: 'users.created_at', title: 'created_at', icon: <CalendarOutlined className="field-icon" /> },
      { key: 'users.is_active', title: 'is_active', icon: <BooleanOutlined className="field-icon" /> },
    ]
  },
  {
    key: 'orders',
    title: 'orders',
    icon: <TableOutlined className="table-icon" />,
    children: [
      { key: 'orders.id', title: 'id', icon: <NumberOutlined className="field-icon" /> },
      { key: 'orders.user_id', title: 'user_id', icon: <NumberOutlined className="field-icon" /> },
      { key: 'orders.total_amount', title: 'total_amount', icon: <NumberOutlined className="field-icon" /> },
      { key: 'orders.status', title: 'status', icon: <FieldStringOutlined className="field-icon" /> },
      { key: 'orders.created_at', title: 'created_at', icon: <CalendarOutlined className="field-icon" /> },
    ]
  },
  {
    key: 'products',
    title: 'products',
    icon: <TableOutlined className="table-icon" />,
    children: [
      { key: 'products.id', title: 'id', icon: <NumberOutlined className="field-icon" /> },
      { key: 'products.name', title: 'name', icon: <FieldStringOutlined className="field-icon" /> },
      { key: 'products.price', title: 'price', icon: <NumberOutlined className="field-icon" /> },
      { key: 'products.category', title: 'category', icon: <FieldStringOutlined className="field-icon" /> },
      { key: 'products.in_stock', title: 'in_stock', icon: <BooleanOutlined className="field-icon" /> },
    ]
  }
];

// 查询模板
const queryTemplates = [
  {
    id: 'select_all',
    name: '查询所有记录',
    description: '从表中查询所有记录',
    sql: 'SELECT * FROM table_name LIMIT 100;'
  },
  {
    id: 'select_with_join',
    name: '关联查询',
    description: '多表关联查询',
    sql: 'SELECT u.name, o.total_amount\nFROM users u\nJOIN orders o ON u.id = o.user_id\nWHERE o.status = \'completed\';'
  },
  {
    id: 'aggregate',
    name: '聚合查询',
    description: '统计和聚合数据',
    sql: 'SELECT category, COUNT(*) as count, AVG(price) as avg_price\nFROM products\nGROUP BY category\nORDER BY count DESC;'
  }
];

const QueryBuilderPage: React.FC = () => {
  const [selectedDataSource, setSelectedDataSource] = useState('postgresql');
  const [selectedTable, setSelectedTable] = useState('');
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [queryConditions, setQueryConditions] = useState<any[]>([]);
  const [generatedSQL, setGeneratedSQL] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [queryResult, setQueryResult] = useState<any>(null);

  // 处理字段选择
  const handleFieldSelect = (selectedKeys: React.Key[]) => {
    setSelectedFields(selectedKeys as string[]);
    generateSQL();
  };

  // 生成SQL查询
  const generateSQL = () => {
    if (!selectedTable || selectedFields.length === 0) {
      setGeneratedSQL('');
      return;
    }

    const fields = selectedFields.map(field => field.split('.')[1]).join(', ');
    let sql = `SELECT ${fields}\nFROM ${selectedTable}`;

    if (queryConditions.length > 0) {
      const conditions = queryConditions
        .map(cond => `${cond.field} ${cond.operator} '${cond.value}'`)
        .join(' AND ');
      sql += `\nWHERE ${conditions}`;
    }

    sql += '\nLIMIT 100;';
    setGeneratedSQL(sql);
  };

  // 执行查询
  const executeQuery = async () => {
    if (!generatedSQL) return;

    setIsExecuting(true);
    try {
      // 模拟查询执行
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockResult = {
        success: true,
        rowCount: 3,
        executionTime: '245ms',
        columns: selectedFields.map(field => field.split('.')[1]),
        data: [
          { id: 1, name: '张三', email: '<EMAIL>' },
          { id: 2, name: '李四', email: '<EMAIL>' },
          { id: 3, name: '王五', email: '<EMAIL>' }
        ]
      };

      setQueryResult(mockResult);
    } catch (error) {
      console.error('Query execution failed:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  // 应用查询模板
  const applyTemplate = (template: any) => {
    setGeneratedSQL(template.sql);
  };

  return (
    <PageContainer>
      {/* 页面头部 */}
      <PageHeader>
        <div className="header-left">
          <Title level={2} className="page-title">查询构建器</Title>
          <div className="page-description">
            可视化构建数据库查询，支持SQL和NoSQL数据库
          </div>
        </div>
        <div className="header-right">
          <Select
            value={selectedDataSource}
            onChange={setSelectedDataSource}
            style={{ width: 150 }}
          >
            <Option value="postgresql">PostgreSQL</Option>
            <Option value="mysql">MySQL</Option>
            <Option value="mongodb">MongoDB</Option>
            <Option value="sqlite">SQLite</Option>
          </Select>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={isExecuting}
            onClick={executeQuery}
            disabled={!generatedSQL}
          >
            执行查询
          </Button>
          <Button icon={<SaveOutlined />}>
            保存查询
          </Button>
        </div>
      </PageHeader>

      {/* 构建器布局 */}
      <BuilderLayout>
        {/* 左侧面板 - 数据源和表结构 */}
        <LeftPanel title="数据源" size="small">
          <div className="panel-section">
            <div className="section-title">
              <DatabaseOutlined />
              表结构
            </div>
            <TableTree
              treeData={mockTables}
              checkable
              onCheck={handleFieldSelect}
              checkedKeys={selectedFields}
              defaultExpandAll
            />
          </div>

          <Divider />

          <div className="panel-section">
            <div className="section-title">
              <FilterOutlined />
              查询模板
            </div>
            <Space direction="vertical" style={{ width: '100%' }}>
              {queryTemplates.map(template => (
                <Card
                  key={template.id}
                  size="small"
                  hoverable
                  onClick={() => applyTemplate(template)}
                  style={{ cursor: 'pointer' }}
                >
                  <div style={{ fontWeight: 600, marginBottom: 4 }}>
                    {template.name}
                  </div>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {template.description}
                  </Text>
                </Card>
              ))}
            </Space>
          </div>
        </LeftPanel>

        {/* 主面板 - 查询构建器 */}
        <MainPanel title="查询构建器" size="small">
          <div className="builder-content">
            <Tabs defaultActiveKey="visual">
              <TabPane tab="可视化构建" key="visual">
                <QueryBuilder />
              </TabPane>
              <TabPane tab="SQL编辑器" key="sql">
                <Input.TextArea
                  value={generatedSQL}
                  onChange={(e) => setGeneratedSQL(e.target.value)}
                  placeholder="在这里编写SQL查询..."
                  style={{
                    height: 300,
                    fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                    fontSize: 14
                  }}
                />
              </TabPane>
            </Tabs>

            {generatedSQL && (
              <QueryPreview>
                <div className="preview-header">
                  <div className="preview-title">生成的SQL查询</div>
                  <Space>
                    <Button
                      size="small"
                      icon={<PlayCircleOutlined />}
                      type="primary"
                      loading={isExecuting}
                      onClick={executeQuery}
                    >
                      执行
                    </Button>
                    <Button size="small">格式化</Button>
                  </Space>
                </div>
                <div className="preview-code">{generatedSQL}</div>
              </QueryPreview>
            )}
          </div>
        </MainPanel>

        {/* 右侧面板 - 查询结果 */}
        <RightPanel title="查询结果" size="small">
          {queryResult ? (
            <div>
              <Space style={{ marginBottom: 16 }}>
                <Tag color="success">查询成功</Tag>
                <Text type="secondary">
                  {queryResult.rowCount} 条记录 | {queryResult.executionTime}
                </Text>
              </Space>

              <Table
                columns={queryResult.columns?.map((col: string) => ({
                  title: col,
                  dataIndex: col,
                  key: col,
                  ellipsis: true,
                }))}
                dataSource={queryResult.data}
                pagination={false}
                size="small"
                scroll={{ y: 400 }}
                rowKey="id"
              />
            </div>
          ) : (
            <Alert
              type="info"
              message="暂无查询结果"
              description="构建查询并执行后，结果将在这里显示"
              showIcon
            />
          )}
        </RightPanel>
      </BuilderLayout>
    </PageContainer>
  );
};

export default QueryBuilderPage;
