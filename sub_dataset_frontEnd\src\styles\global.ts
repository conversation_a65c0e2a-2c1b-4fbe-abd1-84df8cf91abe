import styled, { createGlobalStyle } from 'styled-components';

// 全局样式，仅作用于数据集微应用
export const GlobalStyle = createGlobalStyle`
  .dataset-app {
    /* 样式重置，避免与主应用冲突 */
    * {
      box-sizing: border-box;
    }
    
    /* CSS变量定义 */
    --dataset-primary: #1890ff;
    --dataset-success: #52c41a;
    --dataset-warning: #faad14;
    --dataset-error: #ff4d4f;
    --dataset-text: #262626;
    --dataset-text-secondary: #8c8c8c;
    --dataset-text-disabled: #bfbfbf;
    --dataset-bg: #ffffff;
    --dataset-bg-secondary: #fafafa;
    --dataset-bg-disabled: #f5f5f5;
    --dataset-border: #d9d9d9;
    --dataset-border-light: #f0f0f0;
    --dataset-border-radius: 6px;
    --dataset-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --dataset-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
    --dataset-shadow-modal: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    /* 字体设置 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-size: 14px;
    line-height: 1.5715;
    color: var(--dataset-text);
    
    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: var(--dataset-bg-secondary);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--dataset-border);
      border-radius: 4px;
      
      &:hover {
        background: var(--dataset-text-secondary);
      }
    }
    
    /* 选择文本样式 */
    ::selection {
      background: rgba(24, 144, 255, 0.2);
      color: var(--dataset-text);
    }
    
    /* 焦点样式 */
    :focus-visible {
      outline: 2px solid var(--dataset-primary);
      outline-offset: 2px;
    }
  }
`;

// 应用容器样式
export const AppContainer = styled.div`
  min-height: 100vh;
  background: var(--dataset-bg-secondary);
  
  /* 确保样式隔离 */
  &.dataset-app {
    isolation: isolate;
  }
`;

// 页面容器样式
export const PageContainer = styled.div`
  padding: 24px;
  background: var(--dataset-bg-secondary);
  min-height: calc(100vh - 64px);
`;

// 内容容器样式
export const ContentContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
`;

// 卡片容器样式
export const CardContainer = styled.div`
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  box-shadow: var(--dataset-shadow);
  padding: 24px;
  margin-bottom: 16px;
  
  &:hover {
    box-shadow: var(--dataset-shadow-hover);
  }
`;

// 分割线样式
export const Divider = styled.div`
  height: 1px;
  background: var(--dataset-border-light);
  margin: 16px 0;
`;

// 加载容器样式
export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: var(--dataset-bg);
`;

// 空状态容器样式
export const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 48px 24px;
  text-align: center;
  background: var(--dataset-bg);
  
  .empty-icon {
    font-size: 64px;
    color: var(--dataset-text-disabled);
    margin-bottom: 16px;
  }
  
  .empty-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-text);
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    color: var(--dataset-text-secondary);
    margin-bottom: 24px;
    max-width: 400px;
  }
`;

// 错误容器样式
export const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 48px 24px;
  text-align: center;
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-error);
  border-radius: var(--dataset-border-radius);
  
  .error-icon {
    font-size: 64px;
    color: var(--dataset-error);
    margin-bottom: 16px;
  }
  
  .error-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-error);
    margin-bottom: 8px;
  }
  
  .error-message {
    font-size: 14px;
    color: var(--dataset-text-secondary);
    margin-bottom: 24px;
    max-width: 400px;
    word-break: break-word;
  }
`;

// 工具栏样式
export const Toolbar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: var(--dataset-bg);
  border-bottom: 1px solid var(--dataset-border);
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .toolbar-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dataset-text);
    margin: 0;
  }
`;

// 侧边栏样式
export const Sidebar = styled.div<{ collapsed?: boolean }>`
  width: ${props => props.collapsed ? '64px' : '280px'};
  min-width: ${props => props.collapsed ? '64px' : '280px'};
  height: 100vh;
  background: var(--dataset-bg);
  border-right: 1px solid var(--dataset-border);
  transition: width 0.3s ease;
  overflow: hidden;
`;

// 主内容区域样式
export const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

// 响应式网格样式
export const ResponsiveGrid = styled.div<{ columns?: number; gap?: string }>`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.gap || '16px'};

  @media (min-width: 768px) {
    grid-template-columns: repeat(${props => props.columns || 2}, 1fr);
  }

  @media (min-width: 1200px) {
    grid-template-columns: repeat(${props => Math.min(props.columns || 3, 4)}, 1fr);
  }
`;

// 动画样式
export const FadeIn = styled.div<{ delay?: number }>`
  animation: fadeIn 0.3s ease-in-out;
  animation-delay: ${props => props.delay || 0}ms;
  animation-fill-mode: both;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

// 悬浮卡片样式
export const HoverCard = styled.div`
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--dataset-shadow-hover);
    border-color: var(--dataset-primary);
  }

  &:active {
    transform: translateY(0);
  }
`;

// 状态指示器样式
export const StatusIndicator = styled.div<{ status: 'success' | 'error' | 'warning' | 'info' }>`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'success':
        return `
          background: rgba(82, 196, 26, 0.1);
          color: var(--dataset-success);
          border: 1px solid rgba(82, 196, 26, 0.2);
        `;
      case 'error':
        return `
          background: rgba(255, 77, 79, 0.1);
          color: var(--dataset-error);
          border: 1px solid rgba(255, 77, 79, 0.2);
        `;
      case 'warning':
        return `
          background: rgba(250, 173, 20, 0.1);
          color: var(--dataset-warning);
          border: 1px solid rgba(250, 173, 20, 0.2);
        `;
      case 'info':
      default:
        return `
          background: rgba(24, 144, 255, 0.1);
          color: var(--dataset-primary);
          border: 1px solid rgba(24, 144, 255, 0.2);
        `;
    }
  }}

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
  }
`;

// 代码块样式
export const CodeBlock = styled.pre`
  background: var(--dataset-bg-secondary);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  padding: 16px;
  margin: 16px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: var(--dataset-text);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;

  &::-webkit-scrollbar {
    height: 6px;
  }
`;

// 标签组样式
export const TagGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
`;

// 进度条样式
export const ProgressBar = styled.div<{ progress: number; color?: string }>`
  width: 100%;
  height: 8px;
  background: var(--dataset-bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: ${props => Math.min(Math.max(props.progress, 0), 100)}%;
    background: ${props => props.color || 'var(--dataset-primary)'};
    border-radius: 4px;
    transition: width 0.3s ease;
  }
`;

// 浮动操作按钮样式
export const FloatingActionButton = styled.button`
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--dataset-primary);
  color: white;
  border: none;
  box-shadow: var(--dataset-shadow-hover);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }
`;

// 面包屑导航样式
export const Breadcrumb = styled.nav`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  font-size: 14px;

  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--dataset-text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: var(--dataset-primary);
    }

    &.active {
      color: var(--dataset-text);
      font-weight: 500;
    }
  }

  .breadcrumb-separator {
    color: var(--dataset-text-disabled);
    font-size: 12px;
  }
`;

// 搜索高亮样式
export const SearchHighlight = styled.span`
  background: rgba(255, 235, 59, 0.3);
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
`;

// 工具提示样式
export const TooltipContent = styled.div`
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 8px 12px;
  border-radius: var(--dataset-border-radius);
  font-size: 12px;
  max-width: 250px;
  word-wrap: break-word;
  box-shadow: var(--dataset-shadow);
`;

// 拖拽区域样式
export const DropZone = styled.div<{ isDragOver?: boolean }>`
  border: 2px dashed ${props => props.isDragOver ? 'var(--dataset-primary)' : 'var(--dataset-border)'};
  border-radius: var(--dataset-border-radius);
  padding: 40px 20px;
  text-align: center;
  background: ${props => props.isDragOver ? 'rgba(24, 144, 255, 0.05)' : 'var(--dataset-bg-secondary)'};
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: var(--dataset-primary);
    background: rgba(24, 144, 255, 0.05);
  }

  .drop-icon {
    font-size: 48px;
    color: ${props => props.isDragOver ? 'var(--dataset-primary)' : 'var(--dataset-text-disabled)'};
    margin-bottom: 16px;
  }

  .drop-text {
    color: ${props => props.isDragOver ? 'var(--dataset-primary)' : 'var(--dataset-text-secondary)'};
    font-size: 16px;
    margin-bottom: 8px;
  }

  .drop-hint {
    color: var(--dataset-text-disabled);
    font-size: 12px;
  }
`;
