// 数据源模拟数据
export interface Datasource {
  id: string;
  name: string;
  type: 'postgresql' | 'mysql' | 'mongodb' | 'redis' | 'elasticsearch' | 'rest_api' | 'graphql';
  description?: string;
  configuration: {
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;
    url?: string;
    headers?: Record<string, string>;
    authentication?: {
      type: 'none' | 'basic' | 'bearer' | 'api_key';
      username?: string;
      password?: string;
      token?: string;
      apiKey?: string;
    };
    ssl?: boolean;
    connectionPoolSize?: number;
    timeout?: number;
  };
  isValid: boolean;
  lastTestedAt?: string;
  createdAt: string;
  updatedAt: string;
  userPermissions: string[];
}

export const mockDatasources: Datasource[] = [
  {
    id: 'ds_1',
    name: '主数据库 (PostgreSQL)',
    type: 'postgresql',
    description: '生产环境主数据库，包含用户、订单、产品等核心数据',
    configuration: {
      host: 'localhost',
      port: 5432,
      database: 'main_db',
      username: 'app_user',
      password: '***',
      ssl: true,
      connectionPoolSize: 10,
      timeout: 30000
    },
    isValid: true,
    lastTestedAt: '2024-01-24T10:00:00Z',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-24T10:00:00Z',
    userPermissions: ['read', 'write', 'execute']
  },
  {
    id: 'ds_2',
    name: '用户API服务',
    type: 'rest_api',
    description: '用户管理微服务API，提供用户相关的CRUD操作',
    configuration: {
      url: 'https://api.example.com/users',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      authentication: {
        type: 'bearer',
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      },
      timeout: 10000
    },
    isValid: true,
    lastTestedAt: '2024-01-24T09:30:00Z',
    createdAt: '2024-01-12T14:20:00Z',
    updatedAt: '2024-01-24T09:30:00Z',
    userPermissions: ['read', 'execute']
  },
  {
    id: 'ds_3',
    name: '缓存数据库 (Redis)',
    type: 'redis',
    description: 'Redis缓存数据库，用于会话管理和临时数据存储',
    configuration: {
      host: 'redis.example.com',
      port: 6379,
      password: '***',
      ssl: false,
      timeout: 5000
    },
    isValid: true,
    lastTestedAt: '2024-01-24T08:45:00Z',
    createdAt: '2024-01-15T11:30:00Z',
    updatedAt: '2024-01-24T08:45:00Z',
    userPermissions: ['read', 'write', 'execute']
  },
  {
    id: 'ds_4',
    name: '文档数据库 (MongoDB)',
    type: 'mongodb',
    description: 'MongoDB文档数据库，存储日志、配置和非结构化数据',
    configuration: {
      host: 'mongodb.example.com',
      port: 27017,
      database: 'logs_db',
      username: 'mongo_user',
      password: '***',
      ssl: true,
      timeout: 15000
    },
    isValid: false,
    lastTestedAt: '2024-01-23T16:20:00Z',
    createdAt: '2024-01-18T13:15:00Z',
    updatedAt: '2024-01-23T16:20:00Z',
    userPermissions: ['read']
  },
  {
    id: 'ds_5',
    name: 'GraphQL API',
    type: 'graphql',
    description: 'GraphQL API服务，提供灵活的数据查询接口',
    configuration: {
      url: 'https://api.example.com/graphql',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123'
      },
      authentication: {
        type: 'bearer',
        token: 'graphql_token_123'
      },
      timeout: 20000
    },
    isValid: true,
    lastTestedAt: '2024-01-24T11:15:00Z',
    createdAt: '2024-01-20T10:45:00Z',
    updatedAt: '2024-01-24T11:15:00Z',
    userPermissions: ['read', 'execute']
  },
  {
    id: 'ds_6',
    name: '搜索引擎 (Elasticsearch)',
    type: 'elasticsearch',
    description: 'Elasticsearch搜索引擎，用于全文搜索和日志分析',
    configuration: {
      host: 'elasticsearch.example.com',
      port: 9200,
      username: 'elastic',
      password: '***',
      ssl: true,
      timeout: 30000
    },
    isValid: true,
    lastTestedAt: '2024-01-24T07:30:00Z',
    createdAt: '2024-01-22T15:00:00Z',
    updatedAt: '2024-01-24T07:30:00Z',
    userPermissions: ['read', 'execute']
  }
];

// 数据源连接测试结果
export interface DatasourceTestResult {
  datasourceId: string;
  isSuccess: boolean;
  message: string;
  details?: {
    responseTime: number;
    version?: string;
    features?: string[];
  };
  testedAt: string;
}

export const mockDatasourceTestResults: Record<string, DatasourceTestResult> = {
  ds_1: {
    datasourceId: 'ds_1',
    isSuccess: true,
    message: '连接成功',
    details: {
      responseTime: 45,
      version: 'PostgreSQL 14.5',
      features: ['SSL', 'Connection Pooling', 'Prepared Statements']
    },
    testedAt: '2024-01-24T10:00:00Z'
  },
  ds_2: {
    datasourceId: 'ds_2',
    isSuccess: true,
    message: 'API连接正常',
    details: {
      responseTime: 120,
      version: 'v2.1.0',
      features: ['REST API', 'JWT Authentication', 'Rate Limiting']
    },
    testedAt: '2024-01-24T09:30:00Z'
  },
  ds_3: {
    datasourceId: 'ds_3',
    isSuccess: true,
    message: 'Redis连接成功',
    details: {
      responseTime: 15,
      version: 'Redis 7.0.5',
      features: ['Persistence', 'Clustering', 'Pub/Sub']
    },
    testedAt: '2024-01-24T08:45:00Z'
  },
  ds_4: {
    datasourceId: 'ds_4',
    isSuccess: false,
    message: '连接超时，请检查网络连接和认证信息',
    details: {
      responseTime: 30000
    },
    testedAt: '2024-01-23T16:20:00Z'
  },
  ds_5: {
    datasourceId: 'ds_5',
    isSuccess: true,
    message: 'GraphQL端点可访问',
    details: {
      responseTime: 200,
      version: 'GraphQL 16.6.0',
      features: ['Introspection', 'Subscriptions', 'Federation']
    },
    testedAt: '2024-01-24T11:15:00Z'
  },
  ds_6: {
    datasourceId: 'ds_6',
    isSuccess: true,
    message: 'Elasticsearch集群健康',
    details: {
      responseTime: 80,
      version: 'Elasticsearch 8.5.0',
      features: ['Full-text Search', 'Aggregations', 'Machine Learning']
    },
    testedAt: '2024-01-24T07:30:00Z'
  }
};

// 数据源统计信息
export interface DatasourceStats {
  datasourceId: string;
  totalActions: number;
  successfulExecutions: number;
  failedExecutions: number;
  avgResponseTime: number;
  lastUsed: string;
  popularActions: Array<{
    actionId: string;
    actionName: string;
    executionCount: number;
  }>;
}

export const mockDatasourceStats: Record<string, DatasourceStats> = {
  ds_1: {
    datasourceId: 'ds_1',
    totalActions: 15,
    successfulExecutions: 1247,
    failedExecutions: 23,
    avgResponseTime: 156,
    lastUsed: '2024-01-24T10:30:00Z',
    popularActions: [
      { actionId: 'action_1', actionName: '获取用户列表', executionCount: 456 },
      { actionId: 'action_3', actionName: '订单统计查询', executionCount: 234 },
      { actionId: 'action_7', actionName: '产品库存查询', executionCount: 189 }
    ]
  },
  ds_2: {
    datasourceId: 'ds_2',
    totalActions: 8,
    successfulExecutions: 892,
    failedExecutions: 12,
    avgResponseTime: 245,
    lastUsed: '2024-01-24T09:45:00Z',
    popularActions: [
      { actionId: 'action_2', actionName: '创建用户', executionCount: 234 },
      { actionId: 'action_6', actionName: '用户认证', executionCount: 456 },
      { actionId: 'action_8', actionName: '更新用户信息', executionCount: 123 }
    ]
  }
};
