// 模板API服务
interface Template {
  id: string;
  name: string;
  description: string;
  type: 'API' | 'DB' | 'JS';
  category: string;
  tags: string[];
  isStarred: boolean;
  usageCount: number;
  createdAt: string;
  code: string;
  variables: Array<{
    name: string;
    type: string;
    description: string;
  }>;
}

// 模拟模板数据
const mockTemplates: Template[] = [
  {
    id: 'template_1',
    name: '用户列表查询',
    description: '查询用户列表，支持分页和搜索',
    type: 'API',
    category: 'REST API',
    tags: ['用户', '分页', '搜索'],
    isStarred: true,
    usageCount: 156,
    createdAt: '2024-01-15',
    code: `{
  "method": "GET",
  "path": "/api/users",
  "params": {
    "page": "{{Table1.pageNo}}",
    "limit": "{{Table1.pageSize}}",
    "search": "{{SearchInput.text}}"
  }
}`,
    variables: [
      { name: 'page', type: 'number', description: '页码' },
      { name: 'limit', type: 'number', description: '每页数量' },
      { name: 'search', type: 'string', description: '搜索关键词' }
    ]
  },
  {
    id: 'template_2',
    name: '订单统计查询',
    description: 'SQL查询订单统计数据，按日期分组',
    type: 'DB',
    category: 'SQL',
    tags: ['订单', '统计', '分组'],
    isStarred: false,
    usageCount: 89,
    createdAt: '2024-01-16',
    code: `SELECT 
  DATE(created_at) as order_date,
  COUNT(*) as order_count,
  SUM(total_amount) as total_revenue
FROM orders 
WHERE created_at >= '{{startDate}}'
  AND created_at <= '{{endDate}}'
GROUP BY DATE(created_at)
ORDER BY order_date DESC;`,
    variables: [
      { name: 'startDate', type: 'date', description: '开始日期' },
      { name: 'endDate', type: 'date', description: '结束日期' }
    ]
  },
  {
    id: 'template_3',
    name: '数据处理函数',
    description: 'JavaScript函数用于处理和转换数据',
    type: 'JS',
    category: 'JavaScript',
    tags: ['数据处理', '转换', '验证'],
    isStarred: true,
    usageCount: 234,
    createdAt: '2024-01-17',
    code: `export default {
  processData: (data) => {
    return data.map(item => ({
      ...item,
      fullName: \`\${item.firstName} \${item.lastName}\`,
      isActive: item.status === 'active'
    }));
  },
  
  validateForm: (formData) => {
    const errors = {};
    if (!formData.email) {
      errors.email = '邮箱不能为空';
    }
    return { isValid: Object.keys(errors).length === 0, errors };
  }
};`,
    variables: [
      { name: 'data', type: 'array', description: '输入数据数组' },
      { name: 'formData', type: 'object', description: '表单数据对象' }
    ]
  },
  {
    id: 'template_4',
    name: 'GraphQL产品查询',
    description: 'GraphQL查询产品信息，支持过滤和分页',
    type: 'API',
    category: 'GraphQL',
    tags: ['产品', 'GraphQL', '过滤'],
    isStarred: false,
    usageCount: 67,
    createdAt: '2024-01-18',
    code: `query GetProducts($first: Int!, $filter: ProductFilter) {
  products(first: $first, filter: $filter) {
    edges {
      node {
        id
        name
        price
        category {
          name
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}`,
    variables: [
      { name: 'first', type: 'number', description: '查询数量' },
      { name: 'filter', type: 'object', description: '过滤条件' }
    ]
  },
  {
    id: 'template_5',
    name: '用户认证API',
    description: 'JWT用户认证和授权API调用',
    type: 'API',
    category: 'REST API',
    tags: ['认证', 'JWT', '安全'],
    isStarred: true,
    usageCount: 345,
    createdAt: '2024-01-19',
    code: `{
  "method": "POST",
  "path": "/api/auth/login",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "email": "{{LoginForm.email}}",
    "password": "{{LoginForm.password}}"
  }
}`,
    variables: [
      { name: 'email', type: 'string', description: '用户邮箱' },
      { name: 'password', type: 'string', description: '用户密码' }
    ]
  },
  {
    id: 'template_6',
    name: '数据导出查询',
    description: 'SQL查询用于数据导出，支持多种格式',
    type: 'DB',
    category: 'SQL',
    tags: ['导出', '报表', '数据'],
    isStarred: false,
    usageCount: 123,
    createdAt: '2024-01-20',
    code: `SELECT 
  u.id,
  u.name,
  u.email,
  u.created_at,
  COUNT(o.id) as order_count,
  COALESCE(SUM(o.total_amount), 0) as total_spent
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.created_at >= '{{startDate}}'
  AND u.created_at <= '{{endDate}}'
GROUP BY u.id, u.name, u.email, u.created_at
ORDER BY total_spent DESC;`,
    variables: [
      { name: 'startDate', type: 'date', description: '开始日期' },
      { name: 'endDate', type: 'date', description: '结束日期' }
    ]
  },
  {
    id: 'template_7',
    name: '表单验证器',
    description: '通用表单验证JavaScript函数',
    type: 'JS',
    category: 'JavaScript',
    tags: ['验证', '表单', '工具'],
    isStarred: true,
    usageCount: 456,
    createdAt: '2024-01-21',
    code: `export default {
  validateEmail: (email) => {
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    return emailRegex.test(email);
  },
  
  validatePhone: (phone) => {
    const phoneRegex = /^1[3-9]\\d{9}$/;
    return phoneRegex.test(phone);
  },
  
  validatePassword: (password) => {
    return password.length >= 8 && 
           /[A-Z]/.test(password) && 
           /[a-z]/.test(password) && 
           /\\d/.test(password);
  },
  
  validateForm: (formData, rules) => {
    const errors = {};
    
    Object.keys(rules).forEach(field => {
      const value = formData[field];
      const rule = rules[field];
      
      if (rule.required && (!value || value.trim() === '')) {
        errors[field] = \`\${rule.label}不能为空\`;
      } else if (value && rule.pattern && !rule.pattern.test(value)) {
        errors[field] = rule.message || \`\${rule.label}格式不正确\`;
      }
    });
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};`,
    variables: [
      { name: 'email', type: 'string', description: '邮箱地址' },
      { name: 'phone', type: 'string', description: '手机号码' },
      { name: 'password', type: 'string', description: '密码' },
      { name: 'formData', type: 'object', description: '表单数据' },
      { name: 'rules', type: 'object', description: '验证规则' }
    ]
  },
  {
    id: 'template_8',
    name: 'MongoDB聚合查询',
    description: 'MongoDB聚合管道查询示例',
    type: 'DB',
    category: 'NoSQL',
    tags: ['MongoDB', '聚合', '统计'],
    isStarred: false,
    usageCount: 78,
    createdAt: '2024-01-22',
    code: `db.{{collection}}.aggregate([
  {
    $match: {
      createdAt: {
        $gte: new Date("{{startDate}}"),
        $lte: new Date("{{endDate}}")
      }
    }
  },
  {
    $group: {
      _id: {
        year: { $year: "$createdAt" },
        month: { $month: "$createdAt" },
        day: { $dayOfMonth: "$createdAt" }
      },
      count: { $sum: 1 },
      totalAmount: { $sum: "$amount" }
    }
  },
  {
    $sort: { "_id.year": -1, "_id.month": -1, "_id.day": -1 }
  },
  {
    $limit: {{limit}}
  }
])`,
    variables: [
      { name: 'collection', type: 'string', description: '集合名称' },
      { name: 'startDate', type: 'date', description: '开始日期' },
      { name: 'endDate', type: 'date', description: '结束日期' },
      { name: 'limit', type: 'number', description: '限制数量' }
    ]
  }
];

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模板API服务
export class TemplateAPI {
  // 获取所有模板
  static async getAllTemplates(): Promise<Template[]> {
    await delay(300);
    return [...mockTemplates];
  }

  // 根据ID获取模板
  static async getTemplateById(id: string): Promise<Template | null> {
    await delay(200);
    const template = mockTemplates.find(t => t.id === id);
    return template ? { ...template } : null;
  }

  // 根据类型过滤模板
  static async getTemplatesByType(type: string): Promise<Template[]> {
    await delay(250);
    return mockTemplates.filter(t => t.type === type);
  }

  // 根据分类过滤模板
  static async getTemplatesByCategory(category: string): Promise<Template[]> {
    await delay(250);
    return mockTemplates.filter(t => t.category === category);
  }

  // 搜索模板
  static async searchTemplates(query: string): Promise<Template[]> {
    await delay(300);
    const lowerQuery = query.toLowerCase();
    return mockTemplates.filter(t => 
      t.name.toLowerCase().includes(lowerQuery) ||
      t.description.toLowerCase().includes(lowerQuery) ||
      t.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  // 获取收藏的模板
  static async getStarredTemplates(): Promise<Template[]> {
    await delay(200);
    return mockTemplates.filter(t => t.isStarred);
  }

  // 创建模板
  static async createTemplate(template: Omit<Template, 'id' | 'createdAt' | 'usageCount'>): Promise<Template> {
    await delay(500);
    
    const newTemplate: Template = {
      ...template,
      id: `template_${Date.now()}`,
      createdAt: new Date().toISOString().split('T')[0],
      usageCount: 0,
    };

    mockTemplates.push(newTemplate);
    return { ...newTemplate };
  }

  // 更新模板
  static async updateTemplate(id: string, updates: Partial<Template>): Promise<Template | null> {
    await delay(400);
    
    const index = mockTemplates.findIndex(t => t.id === id);
    if (index === -1) return null;

    const updatedTemplate = {
      ...mockTemplates[index],
      ...updates,
      id, // 确保ID不被覆盖
    };

    mockTemplates[index] = updatedTemplate;
    return { ...updatedTemplate };
  }

  // 删除模板
  static async deleteTemplate(id: string): Promise<boolean> {
    await delay(300);
    
    const index = mockTemplates.findIndex(t => t.id === id);
    if (index === -1) return false;

    mockTemplates.splice(index, 1);
    return true;
  }

  // 切换收藏状态
  static async toggleStar(id: string): Promise<boolean> {
    await delay(200);
    
    const template = mockTemplates.find(t => t.id === id);
    if (!template) return false;

    template.isStarred = !template.isStarred;
    return template.isStarred;
  }

  // 使用模板（增加使用次数）
  static async useTemplate(id: string): Promise<boolean> {
    await delay(150);
    
    const template = mockTemplates.find(t => t.id === id);
    if (!template) return false;

    template.usageCount += 1;
    return true;
  }

  // 获取模板分类列表
  static async getCategories(): Promise<string[]> {
    await delay(100);
    
    const categories = [...new Set(mockTemplates.map(t => t.category))];
    return categories.sort();
  }

  // 获取模板标签列表
  static async getTags(): Promise<string[]> {
    await delay(100);
    
    const tags = [...new Set(mockTemplates.flatMap(t => t.tags))];
    return tags.sort();
  }

  // 获取热门模板
  static async getPopularTemplates(limit: number = 10): Promise<Template[]> {
    await delay(200);
    
    return [...mockTemplates]
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit);
  }

  // 获取最新模板
  static async getLatestTemplates(limit: number = 10): Promise<Template[]> {
    await delay(200);
    
    return [...mockTemplates]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  }
}

export default TemplateAPI;
