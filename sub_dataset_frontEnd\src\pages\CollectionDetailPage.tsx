import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Result,
  Tabs,
  Space,
  Tag,
  Descriptions,
  Alert,
  Spin,
  Row,
  Col,
  Divider,
  List,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Switch
} from 'antd';
import {
  ArrowLeftOutlined,
  PlayCircleOutlined,
  EditOutlined,
  SaveOutlined,
  CopyOutlined,
  DeleteOutlined,
  PlusOutlined,
  FolderOutlined,
  ApiOutlined,
  DatabaseOutlined,
  CodeOutlined,
  SettingOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppDispatch, useAppSelector } from '@/store';
import { fetchCollection, executeCollection } from '@/store/slices/collectionSlice';
import { addNotification } from '@/store/slices/uiSlice';
import { Collection } from '@/types/collection';
import { PageContainer, CardContainer } from '@/styles/global';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

// 样式组件
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .collection-info {
      .collection-title {
        margin: 0;
        color: var(--dataset-text);
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .collection-meta {
        color: var(--dataset-text-secondary);
        font-size: 14px;
        margin-top: 4px;
      }
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
`;

const CollectionTabs = styled(Tabs)`
  .ant-tabs-nav {
    background: var(--dataset-bg);
    margin: 0;
    padding: 0 24px;
    border-bottom: 1px solid var(--dataset-border);

    .ant-tabs-tab {
      padding: 16px 20px;

      &.ant-tabs-tab-active {
        background: var(--dataset-bg-secondary);
      }
    }
  }

  .ant-tabs-content-holder {
    background: var(--dataset-bg-secondary);
  }
`;

const ActionList = styled.div`
  padding: 24px;

  .action-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    margin-bottom: 12px;
    background: var(--dataset-bg);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--dataset-shadow-hover);
    }

    .action-info {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .action-icon {
        font-size: 20px;

        &.api { color: #1890ff; }
        &.db { color: #52c41a; }
        &.js { color: #faad14; }
      }

      .action-details {
        flex: 1;

        .action-name {
          font-weight: 600;
          color: var(--dataset-text);
          margin-bottom: 4px;
        }

        .action-description {
          color: var(--dataset-text-secondary);
          font-size: 12px;
        }
      }
    }

    .action-actions {
      display: flex;
      gap: 8px;
    }
  }
`;

const VariableList = styled.div`
  padding: 24px;

  .variable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    margin-bottom: 8px;
    background: var(--dataset-bg);

    .variable-info {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .variable-key {
        font-weight: 500;
        color: var(--dataset-text);
        min-width: 120px;
      }

      .variable-type {
        font-size: 12px;
      }

      .variable-value {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        color: var(--dataset-text-secondary);
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .variable-actions {
      display: flex;
      gap: 4px;
    }
  }
`;

const ExecutionResult = styled.div`
  padding: 24px;

  .result-summary {
    background: var(--dataset-bg);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 16px;
    margin-bottom: 24px;

    .summary-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .summary-status {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--dataset-primary);
        }

        .stat-label {
          color: var(--dataset-text-secondary);
          font-size: 12px;
        }
      }
    }
  }

  .result-details {
    .result-item {
      margin-bottom: 16px;

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .result-title {
          font-weight: 600;
          color: var(--dataset-text);
        }
      }

      .result-content {
        background: var(--dataset-bg);
        border: 1px solid var(--dataset-border);
        border-radius: var(--dataset-border-radius);
        padding: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
      }
    }
  }
`;

const CollectionDetailPage: React.FC = () => {
  const { collectionId } = useParams<{ collectionId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { items: collections, currentCollection, loading } = useAppSelector(state => state.collections);
  const [activeTab, setActiveTab] = useState('actions');
  const [isExecuting, setIsExecuting] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 获取Collection数据
  useEffect(() => {
    if (collectionId) {
      // 先检查本地是否有数据
      const localCollection = collections[collectionId];
      if (localCollection) {
        dispatch({ type: 'collections/setCurrentCollection', payload: localCollection });
      } else {
        // 从服务器获取
        dispatch(fetchCollection(collectionId));
      }
    }
  }, [collectionId, dispatch, collections]);

  // 执行Collection
  const handleExecuteCollection = async () => {
    if (!currentCollection) return;

    setIsExecuting(true);
    try {
      await dispatch(executeCollection({
        collectionId: currentCollection.id,
        variables: currentCollection.variables?.reduce((acc, variable) => {
          acc[variable.key] = variable.value;
          return acc;
        }, {} as Record<string, any>) || {}
      })).unwrap();

      dispatch(addNotification({
        type: 'success',
        title: '执行成功',
        message: 'Collection执行完成',
      }));

      setActiveTab('results');
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '执行失败',
        message: error as string,
      }));
    } finally {
      setIsExecuting(false);
    }
  };

  // 获取Action图标
  const getActionIcon = (pluginType: string) => {
    switch (pluginType) {
      case 'API':
        return <ApiOutlined className="action-icon api" />;
      case 'DB':
        return <DatabaseOutlined className="action-icon db" />;
      case 'JS':
        return <CodeOutlined className="action-icon js" />;
      default:
        return <ApiOutlined className="action-icon" />;
    }
  };

  if (loading && !currentCollection) {
    return <LoadingSpinner size="large" tip="正在加载Collection详情..." />;
  }

  if (!currentCollection) {
    return (
      <PageContainer>
        <Result
          status="404"
          title="Collection不存在"
          subTitle={`找不到ID为 ${collectionId} 的Collection`}
          extra={
            <Button type="primary" onClick={() => navigate('/collections')}>
              返回Collections列表
            </Button>
          }
        />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      {/* 页面头部 */}
      <PageHeader>
        <div className="header-left">
          <Button
            className="back-button"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/collections')}
          >
            返回
          </Button>

          <div className="collection-info">
            <Title level={3} className="collection-title">
              <FolderOutlined />
              {currentCollection.name}
            </Title>
            <div className="collection-meta">
              ID: {currentCollection.id} |
              Actions: {currentCollection.actions.length} |
              创建时间: {new Date(currentCollection.createdAt).toLocaleString('zh-CN')}
            </div>
          </div>
        </div>

        <div className="header-right">
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={isExecuting}
            onClick={handleExecuteCollection}
          >
            执行Collection
          </Button>
          <Button
            icon={<EditOutlined />}
            onClick={() => setIsEditModalVisible(true)}
          >
            编辑
          </Button>
          <Button icon={<CopyOutlined />}>
            复制
          </Button>
          <Button icon={<SaveOutlined />}>
            保存
          </Button>
        </div>
      </PageHeader>

      {/* Collection标签页 */}
      <CollectionTabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      >
        <TabPane tab={`Actions (${currentCollection.actions.length})`} key="actions">
          <ActionList>
            {currentCollection.actions.length > 0 ? (
              currentCollection.actions.map((actionId, index) => (
                <div key={actionId} className="action-item">
                  <div className="action-info">
                    {getActionIcon('API')}
                    <div className="action-details">
                      <div className="action-name">Action {index + 1}</div>
                      <div className="action-description">
                        Action ID: {actionId}
                      </div>
                    </div>
                    <Tag color="blue">API</Tag>
                  </div>
                  <div className="action-actions">
                    <Button size="small" icon={<PlayCircleOutlined />}>
                      执行
                    </Button>
                    <Button size="small" icon={<EditOutlined />}>
                      编辑
                    </Button>
                    <Button size="small" danger icon={<DeleteOutlined />}>
                      移除
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <Alert
                type="info"
                message="暂无Actions"
                description="点击添加按钮来添加Actions到这个Collection"
                showIcon
                action={
                  <Button size="small" type="primary" icon={<PlusOutlined />}>
                    添加Action
                  </Button>
                }
              />
            )}
          </ActionList>
        </TabPane>

        <TabPane tab={`变量 (${currentCollection.variables?.length || 0})`} key="variables">
          <VariableList>
            {currentCollection.variables && currentCollection.variables.length > 0 ? (
              currentCollection.variables.map((variable, index) => (
                <div key={index} className="variable-item">
                  <div className="variable-info">
                    <div className="variable-key">{variable.key}</div>
                    <Tag className="variable-type" color="geekblue" size="small">
                      {variable.type}
                    </Tag>
                    <div className="variable-value">{variable.value}</div>
                  </div>
                  <div className="variable-actions">
                    <Button size="small" icon={<EditOutlined />} />
                    <Button size="small" danger icon={<DeleteOutlined />} />
                  </div>
                </div>
              ))
            ) : (
              <Alert
                type="info"
                message="暂无变量"
                description="添加变量来在Collection中共享数据"
                showIcon
                action={
                  <Button size="small" type="primary" icon={<PlusOutlined />}>
                    添加变量
                  </Button>
                }
              />
            )}
          </VariableList>
        </TabPane>

        <TabPane tab="基本信息" key="info">
          <div style={{ padding: 24 }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="名称">{currentCollection.name}</Descriptions.Item>
              <Descriptions.Item label="应用ID">{currentCollection.applicationId}</Descriptions.Item>
              <Descriptions.Item label="Actions数量">{currentCollection.actions.length}</Descriptions.Item>
              <Descriptions.Item label="变量数量">{currentCollection.variables?.length || 0}</Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(currentCollection.createdAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(currentCollection.updatedAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
            </Descriptions>

            {currentCollection.description && (
              <div style={{ marginTop: 24 }}>
                <Title level={5}>描述</Title>
                <Text>{currentCollection.description}</Text>
              </div>
            )}

            <div style={{ marginTop: 24 }}>
              <Title level={5}>权限</Title>
              <Space wrap>
                {currentCollection.userPermissions?.map(permission => (
                  <Tag key={permission} color="blue">{permission}</Tag>
                ))}
              </Space>
            </div>
          </div>
        </TabPane>

        <TabPane tab="执行结果" key="results">
          <ExecutionResult>
            <Alert
              type="info"
              message="执行结果"
              description="Collection执行结果将在这里显示。点击上方的执行按钮来运行Collection。"
              showIcon
            />
          </ExecutionResult>
        </TabPane>

        <TabPane tab="执行历史" key="history">
          <div style={{ padding: 24 }}>
            <Alert
              type="info"
              message="执行历史"
              description="Collection的执行历史记录将在这里显示。"
              showIcon
            />
          </div>
        </TabPane>
      </CollectionTabs>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑Collection"
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            name: currentCollection.name,
            description: currentCollection.description,
          }}
        >
          <Form.Item
            label="Collection名称"
            name="name"
            rules={[{ required: true, message: '请输入Collection名称' }]}
          >
            <Input placeholder="输入Collection名称" />
          </Form.Item>

          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea
              rows={4}
              placeholder="输入Collection描述"
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default CollectionDetailPage;
