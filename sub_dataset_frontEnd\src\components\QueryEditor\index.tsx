import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Form,
  Input,
  Select,
  Space,
  Tabs,
  Table,
  Row,
  Col,
  Divider,
  Switch,
  InputNumber,
  Alert,
  Tag,
  Tooltip,
  Dropdown,
  Menu
} from 'antd';
import {
  DatabaseOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  CopyOutlined,
  FormatPainterOutlined,
  HistoryOutlined,
  BookOutlined,
  DownOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { Action, ActionType } from '@/types/action';
import { useAppDispatch } from '@/store';
import { updateActionInPlace } from '@/store/slices/actionSlice';
import { addNotification } from '@/store/slices/uiSlice';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

// 样式组件
const EditorContainer = styled.div`
  padding: 24px;
  background: var(--dataset-bg);
  min-height: 100%;
`;

const EditorTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 24px;
  }
`;

const QueryEditorWrapper = styled.div`
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  overflow: hidden;

  .query-toolbar {
    background: var(--dataset-bg-secondary);
    border-bottom: 1px solid var(--dataset-border);
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .query-editor {
    background: var(--dataset-bg);

    textarea {
      border: none;
      background: transparent;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
      resize: vertical;
      min-height: 300px;

      &:focus {
        box-shadow: none;
      }
    }
  }
`;

const ResultTable = styled(Table)`
  .ant-table-thead > tr > th {
    background: var(--dataset-bg-secondary);
  }

  .ant-table-tbody > tr:hover > td {
    background: var(--dataset-bg-secondary);
  }
`;

const QueryTemplates = styled.div`
  .template-item {
    padding: 12px;
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--dataset-primary);
      background: var(--dataset-bg-secondary);
    }

    .template-title {
      font-weight: 600;
      color: var(--dataset-text);
      margin-bottom: 4px;
    }

    .template-description {
      color: var(--dataset-text-secondary);
      font-size: 12px;
      margin-bottom: 8px;
    }

    .template-code {
      background: var(--dataset-bg-secondary);
      padding: 8px;
      border-radius: var(--dataset-border-radius);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: var(--dataset-text-secondary);
    }
  }
`;

// 查询模板
const SQL_TEMPLATES = [
  {
    id: 'select_all',
    title: '查询所有记录',
    description: '从表中查询所有记录',
    code: 'SELECT * FROM table_name LIMIT 100;'
  },
  {
    id: 'select_where',
    title: '条件查询',
    description: '根据条件查询记录',
    code: 'SELECT * FROM table_name WHERE column_name = \'value\';'
  },
  {
    id: 'insert',
    title: '插入记录',
    description: '向表中插入新记录',
    code: 'INSERT INTO table_name (column1, column2) VALUES (\'value1\', \'value2\');'
  },
  {
    id: 'update',
    title: '更新记录',
    description: '更新表中的记录',
    code: 'UPDATE table_name SET column1 = \'new_value\' WHERE id = 1;'
  },
  {
    id: 'delete',
    title: '删除记录',
    description: '从表中删除记录',
    code: 'DELETE FROM table_name WHERE id = 1;'
  }
];

const NOSQL_TEMPLATES = [
  {
    id: 'find_all',
    title: '查询所有文档',
    description: 'MongoDB查询所有文档',
    code: 'db.collection.find({})'
  },
  {
    id: 'find_where',
    title: '条件查询',
    description: '根据条件查询文档',
    code: 'db.collection.find({ "field": "value" })'
  },
  {
    id: 'insert_one',
    title: '插入文档',
    description: '向集合中插入文档',
    code: 'db.collection.insertOne({ "field1": "value1", "field2": "value2" })'
  },
  {
    id: 'update_one',
    title: '更新文档',
    description: '更新集合中的文档',
    code: 'db.collection.updateOne({ "_id": ObjectId("...") }, { "$set": { "field": "new_value" } })'
  },
  {
    id: 'delete_one',
    title: '删除文档',
    description: '从集合中删除文档',
    code: 'db.collection.deleteOne({ "_id": ObjectId("...") })'
  }
];

interface QueryEditorProps {
  action?: Action;
  queryType?: 'sql' | 'nosql' | 'graphql';
  onSave?: (data: any) => void;
  onCancel?: () => void;
}

const QueryEditor: React.FC<QueryEditorProps> = ({ action, queryType = 'sql', onSave, onCancel }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const [activeTab, setActiveTab] = useState('editor');
  const [query, setQuery] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [queryResult, setQueryResult] = useState<any>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  // 初始化表单数据
  useEffect(() => {
    if (action) {
      form.setFieldsValue({
        name: action.name,
        query: action.actionConfiguration.query || '',
        timeout: action.actionConfiguration.timeout || 30000,
        executeOnLoad: action.executeOnLoad,
      });
      setQuery(action.actionConfiguration.query || '');
    }
  }, [action, form]);

  // 格式化查询
  const formatQuery = () => {
    // 简单的SQL格式化
    if (queryType === 'sql') {
      const formatted = query
        .replace(/\s+/g, ' ')
        .replace(/\s*,\s*/g, ',\n  ')
        .replace(/\s*(SELECT|FROM|WHERE|ORDER BY|GROUP BY|HAVING|LIMIT)\s+/gi, '\n$1 ')
        .replace(/\s*(AND|OR)\s+/gi, '\n  $1 ')
        .trim();
      setQuery(formatted);
    }
  };

  // 执行查询
  const handleExecuteQuery = async () => {
    setIsExecuting(true);
    try {
      // 模拟查询执行
      await new Promise(resolve => setTimeout(resolve, 1200));

      const mockResult = {
        success: true,
        rowCount: 3,
        executionTime: '245ms',
        columns: ['id', 'name', 'email', 'created_at'],
        data: [
          { id: 1, name: '张三', email: '<EMAIL>', created_at: '2024-01-15 10:30:00' },
          { id: 2, name: '李四', email: '<EMAIL>', created_at: '2024-01-16 14:20:00' },
          { id: 3, name: '王五', email: '<EMAIL>', created_at: '2024-01-17 09:15:00' }
        ]
      };

      setQueryResult(mockResult);
      setActiveTab('result');

      dispatch(addNotification({
        type: 'success',
        title: '查询成功',
        message: `查询完成，返回 ${mockResult.rowCount} 条记录`,
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '查询失败',
        message: error as string,
      }));
    } finally {
      setIsExecuting(false);
    }
  };

  // 保存查询
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      const updatedAction = {
        ...action,
        name: values.name,
        executeOnLoad: values.executeOnLoad,
        actionConfiguration: {
          ...action?.actionConfiguration,
          query: values.query,
          timeout: values.timeout,
        },
        timeoutInMillisecond: values.timeout,
        updatedAt: new Date().toISOString(),
      };

      if (action?.id) {
        dispatch(updateActionInPlace({ id: action.id, ...updatedAction }));
      }

      dispatch(addNotification({
        type: 'success',
        title: '保存成功',
        message: '查询配置已保存',
      }));

      if (onSave) {
        onSave(updatedAction);
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '保存失败',
        message: '请检查表单输入',
      }));
    }
  };

  // 应用模板
  const applyTemplate = (template: any) => {
    setQuery(template.code);
    form.setFieldValue('query', template.code);
    setSelectedTemplate(template.id);
  };

  // 获取当前模板列表
  const getCurrentTemplates = () => {
    return queryType === 'sql' ? SQL_TEMPLATES : NOSQL_TEMPLATES;
  };

  // 结果表格列定义
  const resultColumns = queryResult?.columns?.map((col: string) => ({
    title: col,
    dataIndex: col,
    key: col,
    ellipsis: true,
  })) || [];

  return (
    <EditorContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={4} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
          <DatabaseOutlined />
          查询编辑器
          <Tag color={queryType === 'sql' ? 'green' : 'blue'}>
            {queryType.toUpperCase()}
          </Tag>
        </Title>
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={isExecuting}
            onClick={handleExecuteQuery}
            disabled={!query.trim()}
          >
            执行查询
          </Button>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            保存
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={() => {
              navigator.clipboard.writeText(query);
              dispatch(addNotification({
                type: 'success',
                title: '复制成功',
                message: '查询语句已复制到剪贴板',
              }));
            }}
          >
            复制
          </Button>
        </Space>
      </div>

      <EditorTabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="查询编辑器" key="editor">
          <Form form={form} layout="vertical">
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="查询名称"
                  name="name"
                  rules={[{ required: true, message: '请输入查询名称' }]}
                >
                  <Input placeholder="输入查询名称" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="超时时间(ms)"
                  name="timeout"
                >
                  <InputNumber
                    min={1000}
                    max={300000}
                    style={{ width: '100%' }}
                    placeholder="30000"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="页面加载时执行"
                  name="executeOnLoad"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <QueryEditorWrapper>
            <div className="query-toolbar">
              <div className="toolbar-left">
                <Text strong>查询语句</Text>
                <Dropdown
                  overlay={
                    <Menu>
                      {getCurrentTemplates().map(template => (
                        <Menu.Item
                          key={template.id}
                          onClick={() => applyTemplate(template)}
                        >
                          {template.title}
                        </Menu.Item>
                      ))}
                    </Menu>
                  }
                >
                  <Button size="small">
                    <BookOutlined />
                    模板 <DownOutlined />
                  </Button>
                </Dropdown>
              </div>
              <div className="toolbar-right">
                <Tooltip title="格式化查询">
                  <Button
                    size="small"
                    icon={<FormatPainterOutlined />}
                    onClick={formatQuery}
                  />
                </Tooltip>
                <Tooltip title="查询历史">
                  <Button
                    size="small"
                    icon={<HistoryOutlined />}
                  />
                </Tooltip>
              </div>
            </div>
            <div className="query-editor">
              <Form.Item
                name="query"
                rules={[{ required: true, message: '请输入查询语句' }]}
              >
                <TextArea
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder={queryType === 'sql'
                    ? "输入SQL查询语句，例如：\nSELECT * FROM users WHERE status = 'active' LIMIT 10;"
                    : "输入NoSQL查询语句，例如：\ndb.users.find({ status: 'active' }).limit(10)"
                  }
                  autoSize={{ minRows: 12, maxRows: 20 }}
                />
              </Form.Item>
            </div>
          </QueryEditorWrapper>
        </TabPane>

        <TabPane tab="查询结果" key="result">
          {queryResult ? (
            <div>
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Space>
                  <Tag color="success">查询成功</Tag>
                  <Text type="secondary">
                    返回 {queryResult.rowCount} 条记录 | 执行时间: {queryResult.executionTime}
                  </Text>
                </Space>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => {
                    const csvData = [
                      queryResult.columns.join(','),
                      ...queryResult.data.map((row: any) =>
                        queryResult.columns.map((col: string) => row[col]).join(',')
                      )
                    ].join('\n');
                    navigator.clipboard.writeText(csvData);
                    dispatch(addNotification({
                      type: 'success',
                      title: '复制成功',
                      message: '查询结果已复制为CSV格式',
                    }));
                  }}
                >
                  复制结果
                </Button>
              </div>
              <ResultTable
                columns={resultColumns}
                dataSource={queryResult.data}
                pagination={{
                  pageSize: 50,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                }}
                scroll={{ x: true }}
                size="small"
                rowKey="id"
              />
            </div>
          ) : (
            <Alert
              type="info"
              message="暂无查询结果"
              description="点击执行查询按钮来运行查询并查看结果"
              showIcon
            />
          )}
        </TabPane>

        <TabPane tab="查询模板" key="templates">
          <QueryTemplates>
            <Title level={5}>
              {queryType === 'sql' ? 'SQL' : 'NoSQL'} 查询模板
            </Title>
            {getCurrentTemplates().map(template => (
              <div
                key={template.id}
                className="template-item"
                onClick={() => applyTemplate(template)}
              >
                <div className="template-title">{template.title}</div>
                <div className="template-description">{template.description}</div>
                <div className="template-code">{template.code}</div>
              </div>
            ))}
          </QueryTemplates>
        </TabPane>
      </EditorTabs>
    </EditorContainer>
  );
};

export default QueryEditor;
