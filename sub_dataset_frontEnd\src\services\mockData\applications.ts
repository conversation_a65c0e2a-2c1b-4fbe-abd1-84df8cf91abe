// 应用程序模拟数据
export interface Application {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isPublic: boolean;
  slug: string;
  homePageId?: string;
  pages: string[];
  datasources: string[];
  createdAt: string;
  updatedAt: string;
  userPermissions: string[];
  settings: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    enableAnalytics: boolean;
    enableComments: boolean;
  };
}

export interface Page {
  id: string;
  name: string;
  slug: string;
  applicationId: string;
  isHomePage: boolean;
  isHidden: boolean;
  layout: {
    type: 'fixed' | 'fluid' | 'auto';
    maxWidth?: number;
    padding: number;
    backgroundColor?: string;
  };
  widgets: string[];
  actions: string[];
  createdAt: string;
  updatedAt: string;
  userPermissions: string[];
}

export interface Widget {
  id: string;
  name: string;
  type: 'table' | 'form' | 'chart' | 'text' | 'button' | 'input' | 'select' | 'image' | 'container';
  pageId: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  properties: Record<string, any>;
  bindings: Array<{
    property: string;
    actionId?: string;
    expression?: string;
  }>;
  events: Array<{
    type: string;
    actionId?: string;
    callback?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export const mockApplications: Application[] = [
  {
    id: 'app_1',
    name: '用户管理系统',
    description: '企业内部用户管理和权限控制系统',
    icon: '👥',
    color: '#1890ff',
    isPublic: false,
    slug: 'user-management',
    homePageId: 'page_1',
    pages: ['page_1', 'page_2', 'page_3'],
    datasources: ['ds_1', 'ds_2'],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-24T10:30:00Z',
    userPermissions: ['read', 'write', 'execute', 'admin'],
    settings: {
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      enableAnalytics: true,
      enableComments: true
    }
  },
  {
    id: 'app_2',
    name: '订单分析仪表板',
    description: '实时订单数据分析和可视化仪表板',
    icon: '📊',
    color: '#52c41a',
    isPublic: true,
    slug: 'order-dashboard',
    homePageId: 'page_4',
    pages: ['page_4', 'page_5'],
    datasources: ['ds_1', 'ds_3'],
    createdAt: '2024-01-15T14:20:00Z',
    updatedAt: '2024-01-23T16:45:00Z',
    userPermissions: ['read', 'execute'],
    settings: {
      theme: 'dark',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      enableAnalytics: true,
      enableComments: false
    }
  },
  {
    id: 'app_3',
    name: '产品目录管理',
    description: '电商产品信息管理和库存跟踪系统',
    icon: '🛍️',
    color: '#faad14',
    isPublic: false,
    slug: 'product-catalog',
    homePageId: 'page_6',
    pages: ['page_6', 'page_7', 'page_8'],
    datasources: ['ds_1', 'ds_4'],
    createdAt: '2024-01-18T11:30:00Z',
    updatedAt: '2024-01-24T09:15:00Z',
    userPermissions: ['read', 'write', 'execute'],
    settings: {
      theme: 'auto',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      enableAnalytics: false,
      enableComments: true
    }
  }
];

export const mockPages: Page[] = [
  {
    id: 'page_1',
    name: '用户列表',
    slug: 'users',
    applicationId: 'app_1',
    isHomePage: true,
    isHidden: false,
    layout: {
      type: 'fluid',
      maxWidth: 1200,
      padding: 24,
      backgroundColor: '#f5f5f5'
    },
    widgets: ['widget_1', 'widget_2', 'widget_3'],
    actions: ['action_1', 'action_2'],
    createdAt: '2024-01-10T09:30:00Z',
    updatedAt: '2024-01-24T10:00:00Z',
    userPermissions: ['read', 'write']
  },
  {
    id: 'page_2',
    name: '用户详情',
    slug: 'user-detail',
    applicationId: 'app_1',
    isHomePage: false,
    isHidden: false,
    layout: {
      type: 'fixed',
      maxWidth: 800,
      padding: 32,
      backgroundColor: '#ffffff'
    },
    widgets: ['widget_4', 'widget_5'],
    actions: ['action_2', 'action_4'],
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-22T14:30:00Z',
    userPermissions: ['read', 'write']
  },
  {
    id: 'page_3',
    name: '权限管理',
    slug: 'permissions',
    applicationId: 'app_1',
    isHomePage: false,
    isHidden: false,
    layout: {
      type: 'fluid',
      padding: 24
    },
    widgets: ['widget_6'],
    actions: ['action_5'],
    createdAt: '2024-01-12T15:20:00Z',
    updatedAt: '2024-01-20T11:45:00Z',
    userPermissions: ['read', 'admin']
  },
  {
    id: 'page_4',
    name: '订单概览',
    slug: 'overview',
    applicationId: 'app_2',
    isHomePage: true,
    isHidden: false,
    layout: {
      type: 'fluid',
      padding: 16,
      backgroundColor: '#001529'
    },
    widgets: ['widget_7', 'widget_8', 'widget_9'],
    actions: ['action_3'],
    createdAt: '2024-01-15T14:30:00Z',
    updatedAt: '2024-01-23T16:00:00Z',
    userPermissions: ['read']
  },
  {
    id: 'page_5',
    name: '详细报表',
    slug: 'reports',
    applicationId: 'app_2',
    isHomePage: false,
    isHidden: false,
    layout: {
      type: 'fluid',
      padding: 24,
      backgroundColor: '#001529'
    },
    widgets: ['widget_10', 'widget_11'],
    actions: ['action_3', 'action_6'],
    createdAt: '2024-01-16T09:15:00Z',
    updatedAt: '2024-01-23T17:20:00Z',
    userPermissions: ['read']
  }
];

export const mockWidgets: Widget[] = [
  {
    id: 'widget_1',
    name: '用户数据表格',
    type: 'table',
    pageId: 'page_1',
    position: { x: 0, y: 0, width: 12, height: 8 },
    properties: {
      columns: [
        { key: 'id', title: 'ID', dataType: 'number' },
        { key: 'name', title: '姓名', dataType: 'string' },
        { key: 'email', title: '邮箱', dataType: 'string' },
        { key: 'role', title: '角色', dataType: 'string' },
        { key: 'createdAt', title: '创建时间', dataType: 'date' }
      ],
      pagination: true,
      pageSize: 20,
      sortable: true,
      filterable: true,
      selectable: true
    },
    bindings: [
      { property: 'data', actionId: 'action_1' }
    ],
    events: [
      { type: 'onRowClick', actionId: 'action_2' },
      { type: 'onSelectionChange', callback: 'handleSelectionChange' }
    ],
    createdAt: '2024-01-10T09:45:00Z',
    updatedAt: '2024-01-24T10:15:00Z'
  },
  {
    id: 'widget_2',
    name: '搜索框',
    type: 'input',
    pageId: 'page_1',
    position: { x: 0, y: 8, width: 6, height: 1 },
    properties: {
      placeholder: '搜索用户...',
      allowClear: true,
      size: 'large'
    },
    bindings: [],
    events: [
      { type: 'onChange', callback: 'handleSearch' }
    ],
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 'widget_3',
    name: '添加用户按钮',
    type: 'button',
    pageId: 'page_1',
    position: { x: 6, y: 8, width: 2, height: 1 },
    properties: {
      text: '添加用户',
      type: 'primary',
      icon: 'plus',
      size: 'large'
    },
    bindings: [],
    events: [
      { type: 'onClick', callback: 'showCreateUserModal' }
    ],
    createdAt: '2024-01-10T10:15:00Z',
    updatedAt: '2024-01-18T16:20:00Z'
  },
  {
    id: 'widget_4',
    name: '用户信息表单',
    type: 'form',
    pageId: 'page_2',
    position: { x: 0, y: 0, width: 8, height: 10 },
    properties: {
      layout: 'vertical',
      fields: [
        { name: 'name', label: '姓名', type: 'input', required: true },
        { name: 'email', label: '邮箱', type: 'input', required: true },
        { name: 'role', label: '角色', type: 'select', required: true, options: ['admin', 'user', 'guest'] },
        { name: 'avatar', label: '头像', type: 'upload' },
        { name: 'bio', label: '个人简介', type: 'textarea' }
      ]
    },
    bindings: [
      { property: 'initialValues', actionId: 'action_2' }
    ],
    events: [
      { type: 'onSubmit', actionId: 'action_4' },
      { type: 'onReset', callback: 'handleFormReset' }
    ],
    createdAt: '2024-01-10T11:00:00Z',
    updatedAt: '2024-01-22T15:45:00Z'
  }
];

// 应用程序统计信息
export interface ApplicationStats {
  applicationId: string;
  totalPages: number;
  totalActions: number;
  totalWidgets: number;
  totalExecutions: number;
  avgResponseTime: number;
  lastAccessed: string;
  popularPages: Array<{
    pageId: string;
    pageName: string;
    viewCount: number;
  }>;
  activeUsers: number;
  errorRate: number;
}

export const mockApplicationStats: Record<string, ApplicationStats> = {
  app_1: {
    applicationId: 'app_1',
    totalPages: 3,
    totalActions: 5,
    totalWidgets: 6,
    totalExecutions: 1247,
    avgResponseTime: 156,
    lastAccessed: '2024-01-24T10:30:00Z',
    popularPages: [
      { pageId: 'page_1', pageName: '用户列表', viewCount: 456 },
      { pageId: 'page_2', pageName: '用户详情', viewCount: 234 },
      { pageId: 'page_3', pageName: '权限管理', viewCount: 89 }
    ],
    activeUsers: 23,
    errorRate: 1.8
  },
  app_2: {
    applicationId: 'app_2',
    totalPages: 2,
    totalActions: 2,
    totalWidgets: 5,
    totalExecutions: 892,
    avgResponseTime: 245,
    lastAccessed: '2024-01-24T09:45:00Z',
    popularPages: [
      { pageId: 'page_4', pageName: '订单概览', viewCount: 567 },
      { pageId: 'page_5', pageName: '详细报表', viewCount: 234 }
    ],
    activeUsers: 15,
    errorRate: 0.5
  },
  app_3: {
    applicationId: 'app_3',
    totalPages: 3,
    totalActions: 4,
    totalWidgets: 8,
    totalExecutions: 634,
    avgResponseTime: 189,
    lastAccessed: '2024-01-24T08:20:00Z',
    popularPages: [
      { pageId: 'page_6', pageName: '产品列表', viewCount: 345 },
      { pageId: 'page_7', pageName: '库存管理', viewCount: 178 },
      { pageId: 'page_8', pageName: '分类管理', viewCount: 123 }
    ],
    activeUsers: 8,
    errorRate: 2.1
  }
};
