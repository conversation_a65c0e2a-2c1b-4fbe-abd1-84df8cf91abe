import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { Collection, CollectionCreateRequest, CollectionUpdateRequest } from '@/types/collection';
import { PaginatedResponse } from '@/types/common';
import { collectionAPI } from '@/services/api/collectionAPI';

// 异步Actions
export const fetchCollections = createAsyncThunk(
  'collections/fetchCollections',
  async (params: {
    applicationId: string;
    filters?: {
      search?: string;
      page?: number;
      pageSize?: number;
    };
  }) => {
    const response = await collectionAPI.getCollections(params.applicationId, params.filters);
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch collections');
    }
    return response.data;
  }
);

export const fetchCollection = createAsyncThunk(
  'collections/fetchCollection',
  async (id: string) => {
    const response = await collectionAPI.getCollection(id);
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch collection');
    }
    return response.data;
  }
);

export const createCollection = createAsyncThunk(
  'collections/createCollection',
  async (request: CollectionCreateRequest) => {
    const response = await collectionAPI.createCollection(request);
    if (!response.success) {
      throw new Error(response.message || 'Failed to create collection');
    }
    return response.data;
  }
);

export const updateCollection = createAsyncThunk(
  'collections/updateCollection',
  async (params: { id: string; data: CollectionUpdateRequest }) => {
    const response = await collectionAPI.updateCollection(params.id, params.data);
    if (!response.success) {
      throw new Error(response.message || 'Failed to update collection');
    }
    return response.data;
  }
);

export const deleteCollection = createAsyncThunk(
  'collections/deleteCollection',
  async (id: string) => {
    const response = await collectionAPI.deleteCollection(id);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete collection');
    }
    return id;
  }
);

export const duplicateCollection = createAsyncThunk(
  'collections/duplicateCollection',
  async (params: { id: string; newName?: string }) => {
    const response = await collectionAPI.duplicateCollection(params.id, params.newName);
    if (!response.success) {
      throw new Error(response.message || 'Failed to duplicate collection');
    }
    return response.data;
  }
);

export const addActionToCollection = createAsyncThunk(
  'collections/addActionToCollection',
  async (params: { collectionId: string; actionId: string }) => {
    const response = await collectionAPI.addActionToCollection(params.collectionId, params.actionId);
    if (!response.success) {
      throw new Error(response.message || 'Failed to add action to collection');
    }
    return response.data;
  }
);

export const removeActionFromCollection = createAsyncThunk(
  'collections/removeActionFromCollection',
  async (params: { collectionId: string; actionId: string }) => {
    const response = await collectionAPI.removeActionFromCollection(params.collectionId, params.actionId);
    if (!response.success) {
      throw new Error(response.message || 'Failed to remove action from collection');
    }
    return response.data;
  }
);

export const reorderActions = createAsyncThunk(
  'collections/reorderActions',
  async (params: { collectionId: string; actionIds: string[] }) => {
    const response = await collectionAPI.reorderActions(params.collectionId, params.actionIds);
    if (!response.success) {
      throw new Error(response.message || 'Failed to reorder actions');
    }
    return response.data;
  }
);

export const batchDeleteCollections = createAsyncThunk(
  'collections/batchDeleteCollections',
  async (ids: string[]) => {
    const response = await collectionAPI.batchDeleteCollections(ids);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete collections');
    }
    return ids;
  }
);

export const executeCollection = createAsyncThunk(
  'collections/executeCollection',
  async (params: { collectionId: string; variables?: Record<string, any> }) => {
    const response = await collectionAPI.executeCollection(params.collectionId, params.variables);
    if (!response.success) {
      throw new Error(response.message || 'Failed to execute collection');
    }
    return response.data;
  }
);

export const addVariableToCollection = createAsyncThunk(
  'collections/addVariableToCollection',
  async (params: {
    collectionId: string;
    variable: { key: string; value: string; type: string; description?: string }
  }) => {
    const response = await collectionAPI.addVariableToCollection(params.collectionId, params.variable);
    if (!response.success) {
      throw new Error(response.message || 'Failed to add variable to collection');
    }
    return response.data;
  }
);

export const updateCollectionVariable = createAsyncThunk(
  'collections/updateCollectionVariable',
  async (params: {
    collectionId: string;
    variableKey: string;
    variable: { key: string; value: string; type: string; description?: string }
  }) => {
    const response = await collectionAPI.updateCollectionVariable(
      params.collectionId,
      params.variableKey,
      params.variable
    );
    if (!response.success) {
      throw new Error(response.message || 'Failed to update collection variable');
    }
    return response.data;
  }
);

export const removeVariableFromCollection = createAsyncThunk(
  'collections/removeVariableFromCollection',
  async (params: { collectionId: string; variableKey: string }) => {
    const response = await collectionAPI.removeVariableFromCollection(
      params.collectionId,
      params.variableKey
    );
    if (!response.success) {
      throw new Error(response.message || 'Failed to remove variable from collection');
    }
    return response.data;
  }
);

// State接口
interface CollectionState {
  items: Record<string, Collection>;
  currentCollection: Collection | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
  };
  selectedCollectionIds: string[];
  sortBy: 'name' | 'createdAt' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
}

const initialState: CollectionState = {
  items: {},
  currentCollection: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  },
  filters: {},
  selectedCollectionIds: [],
  sortBy: 'updatedAt',
  sortOrder: 'desc',
};

// Slice定义
const collectionSlice = createSlice({
  name: 'collections',
  initialState,
  reducers: {
    setCurrentCollection: (state, action: PayloadAction<Collection | null>) => {
      state.currentCollection = action.payload;
    },
    
    updateCollectionInPlace: (state, action: PayloadAction<Partial<Collection> & { id: string }>) => {
      const { id, ...updates } = action.payload;
      if (state.items[id]) {
        state.items[id] = { ...state.items[id], ...updates };
      }
      if (state.currentCollection?.id === id) {
        state.currentCollection = { ...state.currentCollection, ...updates };
      }
    },
    
    setFilters: (state, action: PayloadAction<Partial<CollectionState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
      // 重置分页
      state.pagination.page = 1;
    },
    
    clearFilters: (state) => {
      state.filters = {};
      state.pagination.page = 1;
    },
    
    setPagination: (state, action: PayloadAction<{ page?: number; pageSize?: number }>) => {
      if (action.payload.page !== undefined) {
        state.pagination.page = action.payload.page;
      }
      if (action.payload.pageSize !== undefined) {
        state.pagination.pageSize = action.payload.pageSize;
      }
    },
    
    setSorting: (state, action: PayloadAction<{ sortBy: CollectionState['sortBy']; sortOrder: CollectionState['sortOrder'] }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    
    setSelectedCollections: (state, action: PayloadAction<string[]>) => {
      state.selectedCollectionIds = action.payload;
    },
    
    toggleCollectionSelection: (state, action: PayloadAction<string>) => {
      const collectionId = action.payload;
      const index = state.selectedCollectionIds.indexOf(collectionId);
      if (index > -1) {
        state.selectedCollectionIds.splice(index, 1);
      } else {
        state.selectedCollectionIds.push(collectionId);
      }
    },
    
    selectAllCollections: (state) => {
      state.selectedCollectionIds = Object.keys(state.items);
    },
    
    clearSelection: (state) => {
      state.selectedCollectionIds = [];
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    builder
      // fetchCollections
      .addCase(fetchCollections.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCollections.fulfilled, (state, action) => {
        state.loading = false;
        const paginatedData = action.payload;
        
        // 更新items
        state.items = paginatedData.items.reduce((acc, collection) => {
          acc[collection.id] = collection;
          return acc;
        }, {} as Record<string, Collection>);
        
        // 更新分页信息
        state.pagination = {
          page: paginatedData.page,
          pageSize: paginatedData.pageSize,
          total: paginatedData.total,
          totalPages: paginatedData.totalPages,
          hasNext: paginatedData.hasNext,
          hasPrev: paginatedData.hasPrev,
        };
      })
      .addCase(fetchCollections.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch collections';
      })
      
      // fetchCollection
      .addCase(fetchCollection.fulfilled, (state, action) => {
        const collectionData = action.payload;
        state.items[collectionData.id] = collectionData;
        state.currentCollection = collectionData;
      })
      
      // createCollection
      .addCase(createCollection.fulfilled, (state, action) => {
        const newCollection = action.payload;
        state.items[newCollection.id] = newCollection;
        state.currentCollection = newCollection;
      })
      
      // updateCollection
      .addCase(updateCollection.fulfilled, (state, action) => {
        const updatedCollection = action.payload;
        state.items[updatedCollection.id] = updatedCollection;
        if (state.currentCollection?.id === updatedCollection.id) {
          state.currentCollection = updatedCollection;
        }
      })
      
      // deleteCollection
      .addCase(deleteCollection.fulfilled, (state, action) => {
        const deletedId = action.payload;
        delete state.items[deletedId];
        if (state.currentCollection?.id === deletedId) {
          state.currentCollection = null;
        }
        // 从选中列表中移除
        state.selectedCollectionIds = state.selectedCollectionIds.filter(id => id !== deletedId);
      })
      
      // duplicateCollection
      .addCase(duplicateCollection.fulfilled, (state, action) => {
        const duplicatedCollection = action.payload;
        state.items[duplicatedCollection.id] = duplicatedCollection;
        state.currentCollection = duplicatedCollection;
      })
      
      // addActionToCollection
      .addCase(addActionToCollection.fulfilled, (state, action) => {
        const updatedCollection = action.payload;
        state.items[updatedCollection.id] = updatedCollection;
        if (state.currentCollection?.id === updatedCollection.id) {
          state.currentCollection = updatedCollection;
        }
      })
      
      // removeActionFromCollection
      .addCase(removeActionFromCollection.fulfilled, (state, action) => {
        const updatedCollection = action.payload;
        state.items[updatedCollection.id] = updatedCollection;
        if (state.currentCollection?.id === updatedCollection.id) {
          state.currentCollection = updatedCollection;
        }
      })
      
      // reorderActions
      .addCase(reorderActions.fulfilled, (state, action) => {
        const updatedCollection = action.payload;
        state.items[updatedCollection.id] = updatedCollection;
        if (state.currentCollection?.id === updatedCollection.id) {
          state.currentCollection = updatedCollection;
        }
      })
      
      // batchDeleteCollections
      .addCase(batchDeleteCollections.fulfilled, (state, action) => {
        const deletedIds = action.payload;
        deletedIds.forEach(id => {
          delete state.items[id];
        });
        if (state.currentCollection && deletedIds.includes(state.currentCollection.id)) {
          state.currentCollection = null;
        }
        state.selectedCollectionIds = [];
      });
  },
});

export const {
  setCurrentCollection,
  updateCollectionInPlace,
  setFilters,
  clearFilters,
  setPagination,
  setSorting,
  setSelectedCollections,
  toggleCollectionSelection,
  selectAllCollections,
  clearSelection,
  clearError,
} = collectionSlice.actions;

export { collectionSlice };
