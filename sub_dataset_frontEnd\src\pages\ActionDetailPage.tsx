import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Result,
  Tabs,
  Space,
  Tag,
  Descriptions,
  Alert,
  Spin,
  Row,
  Col,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  PlayCircleOutlined,
  EditOutlined,
  SaveOutlined,
  CopyOutlined,
  DeleteOutlined,
  ApiOutlined,
  DatabaseOutlined,
  CodeOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { useAppDispatch, useAppSelector } from '@/store';
import { fetchAction, executeAction } from '@/store/slices/actionSlice';
import { addNotification } from '@/store/slices/uiSlice';
import { Action, PluginType, ActionType } from '@/types/action';
import { PageContainer, CardContainer } from '@/styles/global';
import APIEditor from '@/components/APIEditor';
import QueryEditor from '@/components/QueryEditor';
import JSEditor from '@/components/JSEditor';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 样式组件
const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 24px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .action-info {
      .action-title {
        margin: 0;
        color: var(--dataset-text);
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .action-meta {
        color: var(--dataset-text-secondary);
        font-size: 14px;
        margin-top: 4px;
      }
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
`;

const ActionTabs = styled(Tabs)`
  .ant-tabs-nav {
    background: var(--dataset-bg);
    margin: 0;
    padding: 0 24px;
    border-bottom: 1px solid var(--dataset-border);

    .ant-tabs-tab {
      padding: 16px 20px;

      &.ant-tabs-tab-active {
        background: var(--dataset-bg-secondary);
      }
    }
  }

  .ant-tabs-content-holder {
    background: var(--dataset-bg-secondary);
  }
`;

const ActionInfo = styled.div`
  padding: 24px;

  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--dataset-text);
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
`;

// 获取插件图标
const getPluginIcon = (pluginType: PluginType) => {
  switch (pluginType) {
    case PluginType.API:
      return <ApiOutlined />;
    case PluginType.DB:
      return <DatabaseOutlined />;
    case PluginType.JS:
      return <CodeOutlined />;
    default:
      return <ApiOutlined />;
  }
};

// 获取插件颜色
const getPluginColor = (pluginType: PluginType) => {
  switch (pluginType) {
    case PluginType.API:
      return 'blue';
    case PluginType.DB:
      return 'green';
    case PluginType.JS:
      return 'orange';
    default:
      return 'default';
  }
};

const ActionDetailPage: React.FC = () => {
  const { actionId } = useParams<{ actionId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { items: actions, currentAction, loading } = useAppSelector(state => state.actions);
  const [activeTab, setActiveTab] = useState('editor');
  const [isExecuting, setIsExecuting] = useState(false);

  // 获取Action数据
  useEffect(() => {
    if (actionId) {
      // 先检查本地是否有数据
      const localAction = actions[actionId];
      if (localAction) {
        dispatch({ type: 'actions/setCurrentAction', payload: localAction });
      } else {
        // 从服务器获取
        dispatch(fetchAction(actionId));
      }
    }
  }, [actionId, dispatch, actions]);

  // 执行Action
  const handleExecuteAction = async () => {
    if (!currentAction) return;

    setIsExecuting(true);
    try {
      await dispatch(executeAction({
        actionId: currentAction.id,
        params: {}
      })).unwrap();

      dispatch(addNotification({
        type: 'success',
        title: '执行成功',
        message: 'Action执行完成',
      }));

      setActiveTab('response');
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: '执行失败',
        message: error as string,
      }));
    } finally {
      setIsExecuting(false);
    }
  };

  if (loading && !currentAction) {
    return <LoadingSpinner size="large" tip="正在加载Action详情..." />;
  }

  if (!currentAction) {
    return (
      <PageContainer>
        <Result
          status="404"
          title="Action不存在"
          subTitle={`找不到ID为 ${actionId} 的Action`}
          extra={
            <Button type="primary" onClick={() => navigate('/actions')}>
              返回Actions列表
            </Button>
          }
        />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      {/* 页面头部 */}
      <PageHeader>
        <div className="header-left">
          <Button
            className="back-button"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/actions')}
          >
            返回
          </Button>

          <div className="action-info">
            <Title level={3} className="action-title">
              {getPluginIcon(currentAction.pluginType)}
              {currentAction.name}
              <Tag color={getPluginColor(currentAction.pluginType)}>
                {currentAction.pluginType}
              </Tag>
              <Tag color={currentAction.isValid ? 'success' : 'error'}>
                {currentAction.isValid ? '有效' : '无效'}
              </Tag>
            </Title>
            <div className="action-meta">
              ID: {currentAction.id} |
              创建时间: {new Date(currentAction.createdAt).toLocaleString('zh-CN')} |
              更新时间: {new Date(currentAction.updatedAt).toLocaleString('zh-CN')}
            </div>
          </div>
        </div>

        <div className="header-right">
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={isExecuting}
            onClick={handleExecuteAction}
          >
            执行
          </Button>
          <Button icon={<EditOutlined />}>
            编辑
          </Button>
          <Button icon={<CopyOutlined />}>
            复制
          </Button>
          <Button icon={<SaveOutlined />}>
            保存
          </Button>
        </div>
      </PageHeader>

      {/* Action标签页 */}
      <ActionTabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      >
        <TabPane tab="编辑器" key="editor">
          {currentAction.pluginType === PluginType.API && (
            <APIEditor action={currentAction} />
          )}
          {currentAction.pluginType === PluginType.DB && (
            <QueryEditor action={currentAction} />
          )}
          {currentAction.pluginType === PluginType.JS && (
            <JSEditor action={currentAction} />
          )}
        </TabPane>

        <TabPane tab="基本信息" key="info">
          <ActionInfo>
            <div className="info-section">
              <div className="section-title">
                基本信息
              </div>
              <Descriptions column={2} bordered>
                <Descriptions.Item label="名称">{currentAction.name}</Descriptions.Item>
                <Descriptions.Item label="类型">{currentAction.pluginType}</Descriptions.Item>
                <Descriptions.Item label="Action类型">{currentAction.actionType}</Descriptions.Item>
                <Descriptions.Item label="数据源ID">{currentAction.datasourceId || '-'}</Descriptions.Item>
                <Descriptions.Item label="应用ID">{currentAction.applicationId}</Descriptions.Item>
                <Descriptions.Item label="页面ID">{currentAction.pageId || '-'}</Descriptions.Item>
                <Descriptions.Item label="自动执行">
                  <Tag color={currentAction.executeOnLoad ? 'success' : 'default'}>
                    {currentAction.executeOnLoad ? '是' : '否'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="超时时间">{currentAction.timeoutInMillisecond}ms</Descriptions.Item>
              </Descriptions>
            </div>

            {currentAction.invalids && currentAction.invalids.length > 0 && (
              <div className="info-section">
                <div className="section-title">
                  验证错误
                </div>
                <Alert
                  type="error"
                  message="配置验证失败"
                  description={
                    <ul>
                      {currentAction.invalids.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  }
                />
              </div>
            )}

            <div className="info-section">
              <div className="section-title">
                权限信息
              </div>
              <Space wrap>
                {currentAction.userPermissions?.map(permission => (
                  <Tag key={permission} color="blue">{permission}</Tag>
                ))}
              </Space>
            </div>
          </ActionInfo>
        </TabPane>

        <TabPane tab="执行结果" key="response">
          <div style={{ padding: 24 }}>
            <Alert
              type="info"
              message="执行结果"
              description="Action执行结果将在这里显示。点击上方的执行按钮来运行Action。"
              showIcon
            />
          </div>
        </TabPane>

        <TabPane tab="执行历史" key="history">
          <div style={{ padding: 24 }}>
            <Alert
              type="info"
              message="执行历史"
              description="Action的执行历史记录将在这里显示。"
              showIcon
            />
          </div>
        </TabPane>
      </ActionTabs>
    </PageContainer>
  );
};

export default ActionDetailPage;
