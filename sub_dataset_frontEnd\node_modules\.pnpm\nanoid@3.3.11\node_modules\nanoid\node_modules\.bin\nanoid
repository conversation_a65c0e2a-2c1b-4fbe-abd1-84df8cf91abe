#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/bin/node_modules:/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/node_modules:/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/nanoid@3.3.11/node_modules:/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/bin/node_modules:/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/node_modules:/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/nanoid@3.3.11/node_modules:/mnt/d/andy/rebuildPagePlug/sub_dataset_frontEnd/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/nanoid.cjs" "$@"
else
  exec node  "$basedir/../../bin/nanoid.cjs" "$@"
fi
