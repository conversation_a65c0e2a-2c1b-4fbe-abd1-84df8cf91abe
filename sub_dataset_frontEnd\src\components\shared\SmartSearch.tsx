import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Input, AutoComplete, Tag, Dropdown, Menu, Button } from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  CloseOutlined,
  HistoryOutlined,
  StarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

// 样式组件
const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 500px;
`;

const SearchInput = styled(Input)`
  .ant-input {
    padding-left: 40px;
    padding-right: 80px;
  }
`;

const SearchIcon = styled(SearchOutlined)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dataset-text-secondary);
  z-index: 1;
`;

const SearchActions = styled.div`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 4px;
  z-index: 1;
`;

const FilterTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  
  .filter-tag {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
`;

const SearchSuggestions = styled.div`
  .suggestion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    
    .suggestion-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      
      .suggestion-icon {
        color: var(--dataset-text-secondary);
      }
      
      .suggestion-text {
        .suggestion-title {
          font-weight: 500;
          color: var(--dataset-text);
        }
        
        .suggestion-description {
          font-size: 12px;
          color: var(--dataset-text-secondary);
        }
      }
    }
    
    .suggestion-actions {
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover .suggestion-actions {
      opacity: 1;
    }
  }
`;

// 搜索建议接口
interface SearchSuggestion {
  id: string;
  title: string;
  description?: string;
  type: 'action' | 'collection' | 'template' | 'history' | 'filter';
  icon?: React.ReactNode;
  data?: any;
}

// 搜索过滤器接口
interface SearchFilter {
  key: string;
  label: string;
  value: string;
  color?: string;
}

// 智能搜索组件属性
interface SmartSearchProps {
  placeholder?: string;
  onSearch?: (query: string, filters: SearchFilter[]) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  suggestions?: SearchSuggestion[];
  filters?: SearchFilter[];
  showHistory?: boolean;
  showFilters?: boolean;
  className?: string;
}

const SmartSearch: React.FC<SmartSearchProps> = ({
  placeholder = '搜索Actions、Collections、模板...',
  onSearch,
  onSuggestionSelect,
  suggestions = [],
  filters = [],
  showHistory = true,
  showFilters = true,
  className
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [activeFilters, setActiveFilters] = useState<SearchFilter[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchRef = useRef<any>(null);

  // 从localStorage加载搜索历史
  useEffect(() => {
    const history = localStorage.getItem('dataset-search-history');
    if (history) {
      try {
        setSearchHistory(JSON.parse(history));
      } catch (error) {
        console.error('Failed to load search history:', error);
      }
    }
  }, []);

  // 保存搜索历史到localStorage
  const saveSearchHistory = (query: string) => {
    if (!query.trim()) return;
    
    const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('dataset-search-history', JSON.stringify(newHistory));
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    const trimmedValue = value.trim();
    if (trimmedValue) {
      saveSearchHistory(trimmedValue);
      onSearch?.(trimmedValue, activeFilters);
    }
  };

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setSearchValue(value);
    setShowSuggestions(true);
  };

  // 添加过滤器
  const addFilter = (filter: SearchFilter) => {
    if (!activeFilters.find(f => f.key === filter.key && f.value === filter.value)) {
      const newFilters = [...activeFilters, filter];
      setActiveFilters(newFilters);
      onSearch?.(searchValue, newFilters);
    }
  };

  // 移除过滤器
  const removeFilter = (filterToRemove: SearchFilter) => {
    const newFilters = activeFilters.filter(f => 
      !(f.key === filterToRemove.key && f.value === filterToRemove.value)
    );
    setActiveFilters(newFilters);
    onSearch?.(searchValue, newFilters);
  };

  // 清除所有过滤器
  const clearFilters = () => {
    setActiveFilters([]);
    onSearch?.(searchValue, []);
  };

  // 生成搜索建议
  const searchSuggestions = useMemo(() => {
    const allSuggestions: SearchSuggestion[] = [];

    // 添加搜索历史
    if (showHistory && searchHistory.length > 0) {
      searchHistory
        .filter(h => h.toLowerCase().includes(searchValue.toLowerCase()))
        .slice(0, 3)
        .forEach(history => {
          allSuggestions.push({
            id: `history-${history}`,
            title: history,
            type: 'history',
            icon: <HistoryOutlined />
          });
        });
    }

    // 添加外部建议
    suggestions
      .filter(s => 
        s.title.toLowerCase().includes(searchValue.toLowerCase()) ||
        s.description?.toLowerCase().includes(searchValue.toLowerCase())
      )
      .slice(0, 5)
      .forEach(suggestion => {
        allSuggestions.push(suggestion);
      });

    return allSuggestions;
  }, [searchValue, searchHistory, suggestions, showHistory]);

  // 过滤器菜单
  const filterMenu = (
    <Menu>
      <Menu.SubMenu key="type" title="类型">
        <Menu.Item key="api" onClick={() => addFilter({ key: 'type', label: 'API', value: 'api', color: 'blue' })}>
          API
        </Menu.Item>
        <Menu.Item key="db" onClick={() => addFilter({ key: 'type', label: '数据库', value: 'db', color: 'green' })}>
          数据库
        </Menu.Item>
        <Menu.Item key="js" onClick={() => addFilter({ key: 'type', label: 'JavaScript', value: 'js', color: 'orange' })}>
          JavaScript
        </Menu.Item>
      </Menu.SubMenu>
      <Menu.SubMenu key="status" title="状态">
        <Menu.Item key="valid" onClick={() => addFilter({ key: 'status', label: '有效', value: 'valid', color: 'green' })}>
          有效
        </Menu.Item>
        <Menu.Item key="invalid" onClick={() => addFilter({ key: 'status', label: '无效', value: 'invalid', color: 'red' })}>
          无效
        </Menu.Item>
      </Menu.SubMenu>
      <Menu.Divider />
      <Menu.Item key="clear" onClick={clearFilters}>
        清除所有过滤器
      </Menu.Item>
    </Menu>
  );

  // 渲染搜索建议
  const renderSuggestion = (suggestion: SearchSuggestion) => (
    <div
      key={suggestion.id}
      className="suggestion-item"
      onClick={() => {
        if (suggestion.type === 'history') {
          setSearchValue(suggestion.title);
          handleSearch(suggestion.title);
        } else {
          onSuggestionSelect?.(suggestion);
        }
        setShowSuggestions(false);
      }}
    >
      <div className="suggestion-content">
        <span className="suggestion-icon">{suggestion.icon}</span>
        <div className="suggestion-text">
          <div className="suggestion-title">{suggestion.title}</div>
          {suggestion.description && (
            <div className="suggestion-description">{suggestion.description}</div>
          )}
        </div>
      </div>
      <div className="suggestion-actions">
        {suggestion.type === 'history' && (
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              const newHistory = searchHistory.filter(h => h !== suggestion.title);
              setSearchHistory(newHistory);
              localStorage.setItem('dataset-search-history', JSON.stringify(newHistory));
            }}
          />
        )}
      </div>
    </div>
  );

  return (
    <SearchContainer className={className}>
      <SearchIcon />
      
      <AutoComplete
        ref={searchRef}
        value={searchValue}
        onChange={handleInputChange}
        onSearch={handleSearch}
        open={showSuggestions && searchSuggestions.length > 0}
        onDropdownVisibleChange={setShowSuggestions}
        dropdownRender={() => (
          <SearchSuggestions>
            {searchSuggestions.map(renderSuggestion)}
          </SearchSuggestions>
        )}
        style={{ width: '100%' }}
      >
        <SearchInput
          placeholder={placeholder}
          onPressEnter={() => handleSearch(searchValue)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          onFocus={() => setShowSuggestions(true)}
        />
      </AutoComplete>

      <SearchActions>
        {showFilters && (
          <Dropdown overlay={filterMenu} trigger={['click']}>
            <Button
              type="text"
              size="small"
              icon={<FilterOutlined />}
              style={{ 
                color: activeFilters.length > 0 ? 'var(--dataset-primary)' : 'var(--dataset-text-secondary)' 
              }}
            />
          </Dropdown>
        )}
        
        {searchValue && (
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={() => {
              setSearchValue('');
              setShowSuggestions(false);
            }}
          />
        )}
      </SearchActions>

      {/* 活动过滤器 */}
      {activeFilters.length > 0 && (
        <FilterTags>
          {activeFilters.map((filter, index) => (
            <Tag
              key={`${filter.key}-${filter.value}-${index}`}
              className="filter-tag"
              color={filter.color}
              closable
              onClose={() => removeFilter(filter)}
            >
              {filter.label}: {filter.value}
            </Tag>
          ))}
        </FilterTags>
      )}
    </SearchContainer>
  );
};

export default SmartSearch;
