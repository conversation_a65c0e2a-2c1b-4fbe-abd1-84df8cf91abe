{"name": "oblivious-set", "version": "1.0.0", "description": "Like a JavaScript Set() but with a TTL for entries", "license": "MIT", "main": "./dist/lib/index.js", "jsnext:main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/pubkey/oblivious-set"}, "scripts": {"prepublish": "npm run build", "lint": "tslint --project .", "lint:fix": "tslint --project . --fix", "transpile": "tsc -p ./ && echo '# transpile es5 (require) sucess!'", "transpile:es": "tsc -p ./ --target ES5 --module ES6 --outDir ./dist/es && echo '# transpile es (modules) sucess!'", "build": "rimraf -r ./dist && npm run transpile && npm run transpile:es", "test": "mocha -r ts-node/register test/unit.test.ts --timeout 20000 --bail"}, "author": "pubkey", "devDependencies": {"@types/mocha": "8.2.2", "@types/node": "14.17.1", "assert": "2.0.0", "async-test-util": "1.7.3", "mocha": "8.4.0", "rimraf": "3.0.2", "ts-node": "10.0.0", "tslint": "6.1.3", "typescript": "4.3.2"}}