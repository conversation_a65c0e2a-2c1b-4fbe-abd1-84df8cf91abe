import { 
  Collection, 
  CollectionCreateRequest, 
  CollectionUpdateRequest, 
  CollectionExecutionResult,
  CollectionExecutionRequest 
} from '@/types/collection';
import { ApiResponse, PaginatedResponse } from '@/types/common';
import { mockCollections, mockCollectionResults } from '../mockData/collections';

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟API响应包装器
const createApiResponse = <T>(data: T, success = true, message?: string): ApiResponse<T> => ({
  success,
  data,
  message,
  timestamp: new Date().toISOString()
});

// 模拟分页响应
const createPaginatedResponse = <T>(
  items: T[], 
  page = 1, 
  pageSize = 20
): PaginatedResponse<T> => {
  const total = items.length;
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedItems = items.slice(startIndex, endIndex);

  return {
    items: paginatedItems,
    total,
    page,
    pageSize,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

export class CollectionAPI {
  private collections: Collection[] = [...mockCollections];

  // 获取Collection列表
  async getCollections(
    applicationId: string,
    filters?: {
      search?: string;
      page?: number;
      pageSize?: number;
    }
  ): Promise<ApiResponse<PaginatedResponse<Collection>>> {
    await delay(300);

    let filteredCollections = this.collections.filter(collection => 
      collection.applicationId === applicationId
    );

    // 应用搜索过滤器
    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredCollections = filteredCollections.filter(collection => 
        collection.name.toLowerCase().includes(searchLower) ||
        (collection.description && collection.description.toLowerCase().includes(searchLower))
      );
    }

    const paginatedData = createPaginatedResponse(
      filteredCollections,
      filters?.page || 1,
      filters?.pageSize || 20
    );

    return createApiResponse(paginatedData);
  }

  // 获取单个Collection
  async getCollection(id: string): Promise<ApiResponse<Collection>> {
    await delay(200);

    const collection = this.collections.find(c => c.id === id);
    if (!collection) {
      return createApiResponse(null as any, false, `Collection with id ${id} not found`);
    }

    return createApiResponse(collection);
  }

  // 创建Collection
  async createCollection(request: CollectionCreateRequest): Promise<ApiResponse<Collection>> {
    await delay(500);

    const newCollection: Collection = {
      id: `collection_${Date.now()}`,
      name: request.name,
      description: request.description,
      applicationId: request.applicationId,
      actions: request.actions || [],
      variables: request.variables || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userPermissions: ['read', 'execute', 'write']
    };

    this.collections.push(newCollection);
    return createApiResponse(newCollection);
  }

  // 更新Collection
  async updateCollection(id: string, request: CollectionUpdateRequest): Promise<ApiResponse<Collection>> {
    await delay(400);

    const collectionIndex = this.collections.findIndex(c => c.id === id);
    if (collectionIndex === -1) {
      return createApiResponse(null as any, false, `Collection with id ${id} not found`);
    }

    const updatedCollection: Collection = {
      ...this.collections[collectionIndex],
      ...request,
      updatedAt: new Date().toISOString()
    };

    this.collections[collectionIndex] = updatedCollection;
    return createApiResponse(updatedCollection);
  }

  // 删除Collection
  async deleteCollection(id: string): Promise<ApiResponse<void>> {
    await delay(300);

    const collectionIndex = this.collections.findIndex(c => c.id === id);
    if (collectionIndex === -1) {
      return createApiResponse(null as any, false, `Collection with id ${id} not found`);
    }

    this.collections.splice(collectionIndex, 1);
    return createApiResponse(undefined as any);
  }

  // 向Collection添加Action
  async addActionToCollection(collectionId: string, actionId: string): Promise<ApiResponse<Collection>> {
    await delay(300);

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, `Collection with id ${collectionId} not found`);
    }

    if (!collection.actions.includes(actionId)) {
      collection.actions.push(actionId);
      collection.updatedAt = new Date().toISOString();
    }

    return createApiResponse(collection);
  }

  // 从Collection移除Action
  async removeActionFromCollection(collectionId: string, actionId: string): Promise<ApiResponse<Collection>> {
    await delay(300);

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, `Collection with id ${collectionId} not found`);
    }

    collection.actions = collection.actions.filter(id => id !== actionId);
    collection.updatedAt = new Date().toISOString();

    return createApiResponse(collection);
  }

  // 执行Collection
  async executeCollection(request: CollectionExecutionRequest): Promise<ApiResponse<CollectionExecutionResult>> {
    await delay(1500); // 模拟较长的执行时间

    const collection = this.collections.find(c => c.id === request.collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, `Collection with id ${request.collectionId} not found`);
    }

    // 模拟执行结果
    const mockResult = mockCollectionResults[request.collectionId];
    if (mockResult) {
      return createApiResponse(mockResult);
    }

    // 生成默认执行结果
    const defaultResult: CollectionExecutionResult = {
      collectionId: request.collectionId,
      results: {},
      variables: request.variables || {},
      isSuccess: true,
      duration: `${Math.floor(Math.random() * 2000) + 500}ms`,
      executedAt: new Date().toISOString(),
      errors: []
    };

    // 为每个Action生成模拟结果
    collection.actions.forEach(actionId => {
      defaultResult.results[actionId] = {
        isExecutionSuccess: true,
        body: { message: `Action ${actionId} executed successfully` },
        headers: { 'content-type': 'application/json' },
        statusCode: 200,
        isLoading: false,
        dataTypes: ['object'],
        duration: `${Math.floor(Math.random() * 1000) + 100}ms`,
        size: `${Math.floor(Math.random() * 2000) + 200}B`,
        responseDisplayFormat: 'JSON'
      };
    });

    return createApiResponse(defaultResult);
  }

  // 复制Collection
  async duplicateCollection(id: string, newName?: string): Promise<ApiResponse<Collection>> {
    await delay(400);

    const originalCollection = this.collections.find(c => c.id === id);
    if (!originalCollection) {
      return createApiResponse(null as any, false, `Collection with id ${id} not found`);
    }

    const duplicatedCollection: Collection = {
      ...originalCollection,
      id: `collection_${Date.now()}`,
      name: newName || `${originalCollection.name} (副本)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.collections.push(duplicatedCollection);
    return createApiResponse(duplicatedCollection);
  }

  // 重新排序Collection中的Actions
  async reorderActions(collectionId: string, actionIds: string[]): Promise<ApiResponse<Collection>> {
    await delay(300);

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, `Collection with id ${collectionId} not found`);
    }

    // 验证所有actionIds都存在于collection中
    const validActionIds = actionIds.filter(id => collection.actions.includes(id));
    
    collection.actions = validActionIds;
    collection.updatedAt = new Date().toISOString();

    return createApiResponse(collection);
  }

  // 批量删除Collections
  async batchDeleteCollections(ids: string[]): Promise<ApiResponse<void>> {
    await delay(600);

    this.collections = this.collections.filter(collection => !ids.includes(collection.id));
    return createApiResponse(undefined as any);
  }

  // 导出Collection
  async exportCollection(id: string): Promise<ApiResponse<any>> {
    await delay(500);

    const collection = this.collections.find(c => c.id === id);
    if (!collection) {
      return createApiResponse(null as any, false, `Collection with id ${id} not found`);
    }

    const exportData = {
      collection,
      metadata: {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'PagePlug Dataset App'
      }
    };

    return createApiResponse(exportData);
  }

  // 导入Collection
  async importCollection(data: any, applicationId: string): Promise<ApiResponse<Collection>> {
    await delay(600);

    const importedCollection: Collection = {
      ...data.collection,
      id: `collection_${Date.now()}`,
      applicationId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userPermissions: ['read', 'execute', 'write']
    };

    this.collections.push(importedCollection);
    return createApiResponse(importedCollection);
  }

  // 执行Collection
  async executeCollection(
    collectionId: string,
    variables?: Record<string, any>
  ): Promise<ApiResponse<CollectionExecutionResult>> {
    await delay(2000); // 模拟执行时间

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, 'Collection not found');
    }

    // 模拟执行结果
    const mockResult: CollectionExecutionResult = {
      collectionId,
      results: {},
      variables: variables || {},
      isSuccess: Math.random() > 0.1, // 90% 成功率
      duration: `${Math.floor(Math.random() * 2000) + 500}ms`,
      executedAt: new Date().toISOString(),
      errors: []
    };

    // 为每个Action生成模拟结果
    collection.actions.forEach((actionId, index) => {
      const isActionSuccess = Math.random() > 0.05; // 95% 成功率

      mockResult.results[actionId] = {
        isExecutionSuccess: isActionSuccess,
        body: isActionSuccess ? {
          data: `Mock result for action ${actionId}`,
          timestamp: new Date().toISOString()
        } : null,
        headers: { 'content-type': 'application/json' },
        statusCode: isActionSuccess ? 200 : 500,
        isLoading: false,
        dataTypes: ['object'],
        duration: `${Math.floor(Math.random() * 500) + 100}ms`,
        size: `${Math.floor(Math.random() * 10) + 1}KB`,
        responseDisplayFormat: 'JSON'
      };

      if (!isActionSuccess) {
        mockResult.errors.push(`Action ${actionId} failed: Mock error message`);
        mockResult.isSuccess = false;
      }
    });

    return createApiResponse(mockResult);
  }

  // 添加变量到Collection
  async addVariableToCollection(
    collectionId: string,
    variable: { key: string; value: string; type: string; description?: string }
  ): Promise<ApiResponse<Collection>> {
    await delay(300);

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, 'Collection not found');
    }

    // 检查变量是否已存在
    if (collection.variables?.some(v => v.key === variable.key)) {
      return createApiResponse(null as any, false, 'Variable with this key already exists');
    }

    // 添加变量
    if (!collection.variables) {
      collection.variables = [];
    }
    collection.variables.push(variable);
    collection.updatedAt = new Date().toISOString();

    return createApiResponse({ ...collection });
  }

  // 更新Collection变量
  async updateCollectionVariable(
    collectionId: string,
    variableKey: string,
    variable: { key: string; value: string; type: string; description?: string }
  ): Promise<ApiResponse<Collection>> {
    await delay(300);

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, 'Collection not found');
    }

    if (!collection.variables) {
      return createApiResponse(null as any, false, 'Variable not found');
    }

    const variableIndex = collection.variables.findIndex(v => v.key === variableKey);
    if (variableIndex === -1) {
      return createApiResponse(null as any, false, 'Variable not found');
    }

    // 如果key发生变化，检查新key是否已存在
    if (variable.key !== variableKey && collection.variables.some(v => v.key === variable.key)) {
      return createApiResponse(null as any, false, 'Variable with this key already exists');
    }

    // 更新变量
    collection.variables[variableIndex] = variable;
    collection.updatedAt = new Date().toISOString();

    return createApiResponse({ ...collection });
  }

  // 从Collection中移除变量
  async removeVariableFromCollection(
    collectionId: string,
    variableKey: string
  ): Promise<ApiResponse<Collection>> {
    await delay(250);

    const collection = this.collections.find(c => c.id === collectionId);
    if (!collection) {
      return createApiResponse(null as any, false, 'Collection not found');
    }

    if (!collection.variables) {
      return createApiResponse(null as any, false, 'Variable not found');
    }

    const variableIndex = collection.variables.findIndex(v => v.key === variableKey);
    if (variableIndex === -1) {
      return createApiResponse(null as any, false, 'Variable not found');
    }

    // 移除变量
    collection.variables.splice(variableIndex, 1);
    collection.updatedAt = new Date().toISOString();

    return createApiResponse({ ...collection });
  }
}

// 导出单例实例
export const collectionAPI = new CollectionAPI();
