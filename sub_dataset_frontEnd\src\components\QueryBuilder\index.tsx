import React, { useState } from 'react';
import {
  Card,
  Typo<PERSON>,
  Button,
  Select,
  Input,
  Space,
  Row,
  Col,
  Tag,
  Divider,
  Alert,
  Form,
  Switch
} from 'antd';
import {
  DatabaseOutlined,
  PlusOutlined,
  DeleteOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  GroupOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text } = Typography;
const { Option } = Select;

// 样式组件
const BuilderContainer = styled.div`
  padding: 20px;
  background: var(--dataset-bg);
  border-radius: var(--dataset-border-radius);
  min-height: 400px;
`;

const BuilderSection = styled.div`
  margin-bottom: 24px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--dataset-text);
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .section-content {
    background: var(--dataset-bg-secondary);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 16px;
  }
`;

const ConditionItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);

  .condition-field {
    min-width: 120px;
  }

  .condition-operator {
    min-width: 100px;
  }

  .condition-value {
    flex: 1;
  }

  .condition-actions {
    display: flex;
    gap: 4px;
  }
`;

const PreviewArea = styled.div`
  background: var(--dataset-bg-secondary);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  padding: 16px;
  margin-top: 16px;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .preview-title {
      font-weight: 600;
      color: var(--dataset-text);
    }
  }

  .preview-sql {
    background: var(--dataset-bg);
    border: 1px solid var(--dataset-border);
    border-radius: var(--dataset-border-radius);
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    color: var(--dataset-text);
  }
`;

// 操作符选项
const OPERATORS = [
  { value: '=', label: '等于 (=)' },
  { value: '!=', label: '不等于 (!=)' },
  { value: '>', label: '大于 (>)' },
  { value: '>=', label: '大于等于 (>=)' },
  { value: '<', label: '小于 (<)' },
  { value: '<=', label: '小于等于 (<=)' },
  { value: 'LIKE', label: '包含 (LIKE)' },
  { value: 'NOT LIKE', label: '不包含 (NOT LIKE)' },
  { value: 'IN', label: '在列表中 (IN)' },
  { value: 'NOT IN', label: '不在列表中 (NOT IN)' },
  { value: 'IS NULL', label: '为空 (IS NULL)' },
  { value: 'IS NOT NULL', label: '不为空 (IS NOT NULL)' },
];

// 排序方向
const SORT_DIRECTIONS = [
  { value: 'ASC', label: '升序 (ASC)' },
  { value: 'DESC', label: '降序 (DESC)' },
];

interface QueryBuilderProps {
  datasourceId?: string;
  onSave?: (query: any) => void;
  onCancel?: () => void;
}

interface Condition {
  id: string;
  field: string;
  operator: string;
  value: string;
  logicalOperator?: 'AND' | 'OR';
}

interface SortRule {
  id: string;
  field: string;
  direction: 'ASC' | 'DESC';
}

const QueryBuilder: React.FC<QueryBuilderProps> = ({ datasourceId, onSave, onCancel }) => {
  const [selectedTable, setSelectedTable] = useState('');
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [conditions, setConditions] = useState<Condition[]>([]);
  const [sortRules, setSortRules] = useState<SortRule[]>([]);
  const [groupByFields, setGroupByFields] = useState<string[]>([]);
  const [limit, setLimit] = useState<number>(100);
  const [distinct, setDistinct] = useState(false);

  // 可用字段（模拟数据）
  const availableFields = [
    'id', 'name', 'email', 'created_at', 'updated_at', 'status',
    'user_id', 'total_amount', 'order_date', 'category', 'price'
  ];

  // 可用表（模拟数据）
  const availableTables = ['users', 'orders', 'products', 'categories'];

  // 添加条件
  const addCondition = () => {
    const newCondition: Condition = {
      id: `condition_${Date.now()}`,
      field: '',
      operator: '=',
      value: '',
      logicalOperator: conditions.length > 0 ? 'AND' : undefined
    };
    setConditions([...conditions, newCondition]);
  };

  // 删除条件
  const removeCondition = (id: string) => {
    setConditions(conditions.filter(c => c.id !== id));
  };

  // 更新条件
  const updateCondition = (id: string, field: keyof Condition, value: any) => {
    setConditions(conditions.map(c =>
      c.id === id ? { ...c, [field]: value } : c
    ));
  };

  // 添加排序规则
  const addSortRule = () => {
    const newRule: SortRule = {
      id: `sort_${Date.now()}`,
      field: '',
      direction: 'ASC'
    };
    setSortRules([...sortRules, newRule]);
  };

  // 删除排序规则
  const removeSortRule = (id: string) => {
    setSortRules(sortRules.filter(r => r.id !== id));
  };

  // 更新排序规则
  const updateSortRule = (id: string, field: keyof SortRule, value: any) => {
    setSortRules(sortRules.map(r =>
      r.id === id ? { ...r, [field]: value } : r
    ));
  };

  // 生成SQL查询
  const generateSQL = () => {
    if (!selectedTable) return '';

    let sql = 'SELECT ';

    // DISTINCT
    if (distinct) {
      sql += 'DISTINCT ';
    }

    // 字段
    if (selectedFields.length > 0) {
      sql += selectedFields.join(', ');
    } else {
      sql += '*';
    }

    sql += `\nFROM ${selectedTable}`;

    // WHERE条件
    if (conditions.length > 0) {
      const validConditions = conditions.filter(c => c.field && c.value);
      if (validConditions.length > 0) {
        sql += '\nWHERE ';
        sql += validConditions.map((condition, index) => {
          let conditionSQL = '';
          if (index > 0 && condition.logicalOperator) {
            conditionSQL += `${condition.logicalOperator} `;
          }

          if (condition.operator === 'LIKE' || condition.operator === 'NOT LIKE') {
            conditionSQL += `${condition.field} ${condition.operator} '%${condition.value}%'`;
          } else if (condition.operator === 'IN' || condition.operator === 'NOT IN') {
            conditionSQL += `${condition.field} ${condition.operator} (${condition.value})`;
          } else if (condition.operator === 'IS NULL' || condition.operator === 'IS NOT NULL') {
            conditionSQL += `${condition.field} ${condition.operator}`;
          } else {
            conditionSQL += `${condition.field} ${condition.operator} '${condition.value}'`;
          }

          return conditionSQL;
        }).join(' ');
      }
    }

    // GROUP BY
    if (groupByFields.length > 0) {
      sql += `\nGROUP BY ${groupByFields.join(', ')}`;
    }

    // ORDER BY
    if (sortRules.length > 0) {
      const validSortRules = sortRules.filter(r => r.field);
      if (validSortRules.length > 0) {
        sql += '\nORDER BY ';
        sql += validSortRules.map(rule => `${rule.field} ${rule.direction}`).join(', ');
      }
    }

    // LIMIT
    if (limit > 0) {
      sql += `\nLIMIT ${limit}`;
    }

    return sql + ';';
  };

  return (
    <BuilderContainer>
      {/* 基本设置 */}
      <BuilderSection>
        <div className="section-header">
          <div className="section-title">
            <DatabaseOutlined />
            基本设置
          </div>
        </div>
        <div className="section-content">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="选择表">
                <Select
                  value={selectedTable}
                  onChange={setSelectedTable}
                  placeholder="选择数据表"
                  style={{ width: '100%' }}
                >
                  {availableTables.map(table => (
                    <Option key={table} value={table}>{table}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="选择字段">
                <Select
                  mode="multiple"
                  value={selectedFields}
                  onChange={setSelectedFields}
                  placeholder="选择字段（留空表示所有字段）"
                  style={{ width: '100%' }}
                >
                  {availableFields.map(field => (
                    <Option key={field} value={field}>{field}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="去重">
                <Switch
                  checked={distinct}
                  onChange={setDistinct}
                  checkedChildren="DISTINCT"
                  unCheckedChildren="ALL"
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="限制条数">
                <Input
                  type="number"
                  value={limit}
                  onChange={(e) => setLimit(Number(e.target.value))}
                  placeholder="100"
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </BuilderSection>

      {/* 查询条件 */}
      <BuilderSection>
        <div className="section-header">
          <div className="section-title">
            <FilterOutlined />
            查询条件 (WHERE)
          </div>
          <Button
            type="dashed"
            size="small"
            icon={<PlusOutlined />}
            onClick={addCondition}
          >
            添加条件
          </Button>
        </div>
        <div className="section-content">
          {conditions.length > 0 ? (
            conditions.map((condition, index) => (
              <ConditionItem key={condition.id}>
                {index > 0 && (
                  <Select
                    value={condition.logicalOperator}
                    onChange={(value) => updateCondition(condition.id, 'logicalOperator', value)}
                    style={{ width: 80 }}
                  >
                    <Option value="AND">AND</Option>
                    <Option value="OR">OR</Option>
                  </Select>
                )}
                <Select
                  className="condition-field"
                  value={condition.field}
                  onChange={(value) => updateCondition(condition.id, 'field', value)}
                  placeholder="选择字段"
                >
                  {availableFields.map(field => (
                    <Option key={field} value={field}>{field}</Option>
                  ))}
                </Select>
                <Select
                  className="condition-operator"
                  value={condition.operator}
                  onChange={(value) => updateCondition(condition.id, 'operator', value)}
                >
                  {OPERATORS.map(op => (
                    <Option key={op.value} value={op.value}>{op.label}</Option>
                  ))}
                </Select>
                {!['IS NULL', 'IS NOT NULL'].includes(condition.operator) && (
                  <Input
                    className="condition-value"
                    value={condition.value}
                    onChange={(e) => updateCondition(condition.id, 'value', e.target.value)}
                    placeholder="输入值"
                  />
                )}
                <div className="condition-actions">
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeCondition(condition.id)}
                  />
                </div>
              </ConditionItem>
            ))
          ) : (
            <Alert
              type="info"
              message="暂无查询条件"
              description="点击添加条件按钮来设置WHERE条件"
              showIcon
            />
          )}
        </div>
      </BuilderSection>

      {/* 排序规则 */}
      <BuilderSection>
        <div className="section-header">
          <div className="section-title">
            <SortAscendingOutlined />
            排序规则 (ORDER BY)
          </div>
          <Button
            type="dashed"
            size="small"
            icon={<PlusOutlined />}
            onClick={addSortRule}
          >
            添加排序
          </Button>
        </div>
        <div className="section-content">
          {sortRules.length > 0 ? (
            sortRules.map((rule) => (
              <ConditionItem key={rule.id}>
                <Select
                  className="condition-field"
                  value={rule.field}
                  onChange={(value) => updateSortRule(rule.id, 'field', value)}
                  placeholder="选择字段"
                >
                  {availableFields.map(field => (
                    <Option key={field} value={field}>{field}</Option>
                  ))}
                </Select>
                <Select
                  className="condition-operator"
                  value={rule.direction}
                  onChange={(value) => updateSortRule(rule.id, 'direction', value)}
                >
                  {SORT_DIRECTIONS.map(dir => (
                    <Option key={dir.value} value={dir.value}>{dir.label}</Option>
                  ))}
                </Select>
                <div className="condition-actions">
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeSortRule(rule.id)}
                  />
                </div>
              </ConditionItem>
            ))
          ) : (
            <Alert
              type="info"
              message="暂无排序规则"
              description="点击添加排序按钮来设置ORDER BY规则"
              showIcon
            />
          )}
        </div>
      </BuilderSection>

      {/* 分组字段 */}
      <BuilderSection>
        <div className="section-header">
          <div className="section-title">
            <GroupOutlined />
            分组字段 (GROUP BY)
          </div>
        </div>
        <div className="section-content">
          <Select
            mode="multiple"
            value={groupByFields}
            onChange={setGroupByFields}
            placeholder="选择分组字段"
            style={{ width: '100%' }}
          >
            {availableFields.map(field => (
              <Option key={field} value={field}>{field}</Option>
            ))}
          </Select>
        </div>
      </BuilderSection>

      {/* SQL预览 */}
      <PreviewArea>
        <div className="preview-header">
          <div className="preview-title">生成的SQL查询</div>
          <Space>
            <Tag color="blue">预览</Tag>
            <Button size="small" onClick={() => onSave?.(generateSQL())}>
              保存查询
            </Button>
          </Space>
        </div>
        <div className="preview-sql">
          {generateSQL() || '请先配置查询条件...'}
        </div>
      </PreviewArea>
    </BuilderContainer>
  );
};

export default QueryBuilder;
